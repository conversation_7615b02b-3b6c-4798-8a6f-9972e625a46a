import {DataController} from '../../../../base/baseController';
import {
  GameQuizQuestionAPI,
  GetQuestionsResponse,
} from '../../sakutimban/types/sakuTBTypes';
import {Question} from '../models/models';

export class MghhDa {
  private questionController: DataController;
  //GameAnswer
  private answerController: DataController;

  constructor() {
    this.questionController = new DataController('GameQuestion');
    this.answerController = new DataController('GameAnswer');
  }

  validateQuestions = (questions: any): Question[] => {
    return questions.map((question: any) => {
      return {
        id: question.Id,
        level: question.Level,
        title: question.Name,
        listWords: question.Answers.map((answer: any) => ({
          id: answer.Id,
          position: answer.Sort,
          text: answer.Name,
        })),
        sort: question.Sort,
        hint: question.Suggest,
      };
    });
  };

  async getQuestionsByGameAndStage(
    gameId: string,
    stage: number = 1,
    competenceId: string,
  ): Promise<Question[]> {
    try {
      const response: GetQuestionsResponse =
        await this.questionController.getListSimple({
          query: `@GameId: {${gameId}} @Stage: [${stage}] @Purpose: [${competenceId}]`,
          sortby: {BY: 'Sort', DIRECTION: 'ASC'},
        });
      if (response.code === 200) {
        const answerResponse = await this.answerController.getListSimple({
          query: `@GameQuestionId: {${response.data
            .map((q: any) => q.Id)
            .join(' | ')}}`,
        });
        if (
          answerResponse &&
          answerResponse.data &&
          answerResponse.data.length > 0
        ) {
          response.data.forEach((question: any) => {
            question.Answers = answerResponse.data.filter(
              (answer: any) => answer.GameQuestionId === question.Id,
            );
          });
        }

        const questions = response.data || [];
        // Validate questions
        const dataValidate = this.validateQuestions(questions);
        return dataValidate;
      } else {
        throw new Error(
          `API Error: ${response.message} (Code: ${response.code})`,
        );
      }
    } catch (error) {
      console.error('[MghhDa] Error fetching questions:', error);
      throw Error('Failed to fetch questions from API');
    }
  }

  static async getGameConfig(gameId: string): Promise<any> {
    try {
      const controller = new DataController('GameConfig');
      const response = await controller.getListSimple({
        query: `@GameId: {${gameId}}`,
      });
      if (
        response.code !== 200 ||
        !response.data ||
        response.data.length === 0
      ) {
        throw new Error(
          'No game config found or API returned unsuccessful response',
        );
      }
      const configData = response.data.find((item: any) => item.Sort === 1);
      const configLv1 = response.data.find((item: any) => item.Sort === 2);
      const configLv2 = response.data.find((item: any) => item.Sort === 3);
      const configLv3 = response.data.find((item: any) => item.Sort === 4);

      return {
        bonusLv1: configLv1.Score || 0,
        bonusLv2: configLv2.Score || 0,
        bonusLv3: configLv3.Score || 0,
        timeLimit: configData.Time || 450,
      };
    } catch (error) {
      console.error('[GameConfigDA] Error loading game config:', error);
      throw error;
    }
  }
}
