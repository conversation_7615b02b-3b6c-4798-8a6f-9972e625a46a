import {createSlice, createAsyncThunk} from '@reduxjs/toolkit';
import {DHBCDA, DHBCQuestion, DHBCGameConfig} from '../../../modules/game/duoihinhbatchu/da/dhbcDA';

interface Question {
  id: string;
  text: string;
  image: string;
  hint: string;
  answer: string;
}

interface State {
  listQuestion: Question[];
  currentQuestion: Question;
  totalQuestion: number;
  questionDone: number;
  isWinLevel: boolean;
  // API states
  loading: boolean;
  configLoading: boolean;
  error: string | null;
  configError: string | null;
  initialized: boolean;
  configInitialized: boolean;
  noData: boolean;
  // Game config from API
  gameConfig: DHBCGameConfig | null;
  usedHints: string[]
}

const data = [
  {
    id: '1',
    text: 'Đ<PERSON><PERSON> là con gì',
    image:
      'https://pethouse.com.vn/wp-content/uploads/2023/06/cho-pug-1256x800.webp',
    hint: '<PERSON><PERSON><PERSON> là một loài vật có vú và có thể ăn thịt',
    answer: 'con chó',
  },
  {
    id: '2',
    text: 'Đ<PERSON><PERSON> là con gì',
    image:
      'https://topanh.com/wp-content/uploads/2024/02/Hinh-anh-con-khi-dep-nhat.jpg',
    hint: 'Đây là một loài động vật có thể leo trèo',
    answer: 'con khỉ',
  },
];
const initialState: State = {
  listQuestion: [],
  currentQuestion: {} as Question,
  totalQuestion: 0,
  questionDone: 0,
  isWinLevel: false,
  // API states
  loading: false,
  configLoading: false,
  error: null,
  configError: null,
  initialized: false,
  configInitialized: false,
  noData: false,
  // Game config
  gameConfig: null,
  usedHints : []
};

// Async thunk để load game config từ API
export const loadDHBCGameConfig = createAsyncThunk(
  'dhbc/loadGameConfig',
  async ({ gameId }: { gameId: string }) => {
    try {
      const gameConfig = await DHBCDA.getGameConfig(gameId);
      return gameConfig;
    } catch (error) {
      console.error('[Redux] Error loading DHBC game config:', error);
      throw error;
    }
  }
);

// Async thunk để load questions từ API
export const loadDHBCQuestions = createAsyncThunk(
  'dhbc/loadQuestions',
  async ({ gameId, milestoneId, competenceId }: { gameId: string; milestoneId: number; competenceId: string }) => {
    try {
      console.log(`[Redux] Loading DHBC questions for GameId: ${gameId}, Stage: ${milestoneId}, CompetenceId: ${competenceId}`);
      const dhbcDA = new DHBCDA();
      const rawQuestions = await dhbcDA.getQuestionsByGameAndStage(gameId, milestoneId, competenceId);
      if (rawQuestions.length === 0) {
        console.warn('[Redux] No DHBC questions found');
        return {
          questions: [],
          noData: true
        };
      }

      // Transform DHBCQuestion to Question format
      const transformedQuestions: Question[] = rawQuestions.map(q => ({
        id: q.id,
        text: q.text,
        image: q.image,
        hint: q.hint, 
        answer: q.answer,
      }));

      return {
        questions: transformedQuestions,
        noData: false
      };
    } catch (error) {
      console.error('[Redux] Error loading DHBC questions:', error);
      throw error;
    }
  }
);

export const dhbcReducer = createSlice({
  name: 'dhbcReducer',
  initialState,
  reducers: {
    setData(state, action: { payload: { stateName: keyof State; value: any } }) {
      (state as any)[action.payload.stateName] = action.payload.value;
    },
    startGame(state) {
      // Reset game state
      state.questionDone = 0;
      state.isWinLevel = false;
      state.usedHints = [];
      // Nếu có data từ API, sử dụng data đó
      if (state.listQuestion.length > 0) {
        state.currentQuestion = state.listQuestion[0];
        state.totalQuestion = state.listQuestion.length;
      } else {
        // Fallback to hardcode data nếu chưa load từ API
        state.listQuestion = data;
        state.currentQuestion = data[0];
        state.totalQuestion = data.length;
      }
    },
    nextQuestion(state) {
      if (state.questionDone === state.totalQuestion) {
        state.isWinLevel = true;
        return;
      }
      state.currentQuestion = state.listQuestion[state.questionDone];
    },
    reset(_state) {
      // Reset về initial state
      return initialState;
    },
    markHintUsed(state, action) {
      const questionId = action.payload;
      if (!state.usedHints.includes(questionId)) {
        state.usedHints.push(questionId);
      }
    },
  },
  extraReducers: (builder) => {
    // Handle loadDHBCGameConfig
    builder
      .addCase(loadDHBCGameConfig.pending, (state) => {
        state.configLoading = true;
        state.configError = null;
      })
      .addCase(loadDHBCGameConfig.fulfilled, (state, action) => {
        state.configLoading = false;
        state.gameConfig = action.payload;
        state.configInitialized = true;
        state.configError = null;
      })
      .addCase(loadDHBCGameConfig.rejected, (state, action) => {
        state.configLoading = false;
        state.configError = action.error.message || 'Failed to load game config';
        state.configInitialized = false;
      })
      // Handle loadDHBCQuestions
      .addCase(loadDHBCQuestions.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.noData = false;
      })
      .addCase(loadDHBCQuestions.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        state.initialized = true;

        if (action.payload.noData) {
          state.noData = true;
          state.listQuestion = [];
          state.totalQuestion = 0;
        } else {
          state.noData = false;
          state.listQuestion = action.payload.questions;
          state.totalQuestion = action.payload.questions.length;
          state.currentQuestion = action.payload.questions[0] || ({} as Question);
        }
      })
      .addCase(loadDHBCQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to load questions';
        state.initialized = false;
        state.noData = true;
      });
  },
});

export const {setData, reset, startGame, nextQuestion, markHintUsed} = dhbcReducer.actions;

export default dhbcReducer.reducer;
