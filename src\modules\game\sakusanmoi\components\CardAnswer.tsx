import React, {use, useEffect, useRef} from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View, Animated} from 'react-native';
import {SakuSmAnswer} from '../../../../redux/reducers/game/sakuSMReducer';

interface CardAnswerProps {
  answer: SakuSmAnswer;
  index: number;
  side: 'left' | 'right';
  onClick: (status: 'correct' | 'wrong' | null) => void;
  allowChoose: boolean;
  resetKey?: number; // Key để force reset animation
}

const CardAnswer = ({
  answer,
  index,
  side,
  onClick,
  allowChoose,
  resetKey,
}: CardAnswerProps) => {
  const [isCheck, setIsCheck] = React.useState<'correct' | 'wrong' | null>(
    null,
  );

  // Animation values
  const blinkAnimation = useRef(new Animated.Value(1)).current;
  const backgroundColorAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Reset khi answer thay đổi hoặc resetKey thay đổi
    setIsCheck(null);
    blinkAnimation.setValue(1);
    backgroundColorAnimation.setValue(0);
  }, [answer, resetKey]);

  useEffect(() => {
    return () => {
      setIsCheck(null);
      // Reset animations
      blinkAnimation.setValue(1);
      backgroundColorAnimation.setValue(0);
    };
  }, []);

  // Animation functions
  const startCorrectAnimation = () => {
    // Hiệu ứng nháy xanh như game Ai là triệu phú
    setIsCheck('correct');
    backgroundColorAnimation.setValue(1);

    Animated.sequence([
      Animated.timing(blinkAnimation, {
        toValue: 0.3,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(blinkAnimation, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(blinkAnimation, {
        toValue: 0.3,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(blinkAnimation, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const startWrongAnimation = () => {
    // Hiệu ứng nháy đỏ rồi reset về bình thường
    setIsCheck('wrong');
    backgroundColorAnimation.setValue(2);

    Animated.sequence([
      Animated.timing(blinkAnimation, {
        toValue: 0.3,
        duration: 150,
        useNativeDriver: false,
      }),
      Animated.timing(blinkAnimation, {
        toValue: 1,
        duration: 150,
        useNativeDriver: false,
      }),
      Animated.timing(blinkAnimation, {
        toValue: 0.3,
        duration: 150,
        useNativeDriver: false,
      }),
      Animated.timing(blinkAnimation, {
        toValue: 1,
        duration: 150,
        useNativeDriver: false,
      }),
    ]).start(() => {
      // Reset về trạng thái bình thường sau khi nháy xong
      setTimeout(() => {
        setIsCheck(null);
        backgroundColorAnimation.setValue(0);
        blinkAnimation.setValue(1);
      }, 200);
    });
  };

  const handleClick = () => {
    if (!allowChoose) return;
    if (answer.isTrue) {
      startCorrectAnimation();
      onClick('correct');
    } else {
      startWrongAnimation();
      onClick('wrong');
    }
  };
  const getImage = (index: number) => {
    switch (index) {
      case 0:
        return require('../assets/worm_1.png');
      case 1:
        return require('../assets/worm_2.png');
      case 2:
        return require('../assets/worm_3.png');
      case 3:
        return require('../assets/worm_4.png');
      default:
        return require('../assets/worm_1.png');
    }
  };

  const image = getImage(index);

  // Dynamic background color based on animation value
  const animatedBackgroundColor = backgroundColorAnimation.interpolate({
    inputRange: [0, 1, 2],
    outputRange: ['#FCF8E8', '#27e844', '#fc3030'], // normal, correct, wrong
  });

  return (
    <View
      style={{
        position: 'relative',
        marginVertical: 6,
        width: '100%',
        paddingHorizontal: side === 'left' ? 60 : 60, // Để chỗ cho icon con sâu
      }}>
      <TouchableOpacity
        onPress={allowChoose ? handleClick : undefined}
        activeOpacity={0.8}
        style={{width: '100%'}}>
        <Animated.View
          style={[
            styles.card,
            {
              backgroundColor: animatedBackgroundColor,
              opacity: blinkAnimation,
            },
          ]}>
          <Text style={[styles.text]}>{answer.text}</Text>
        </Animated.View>
      </TouchableOpacity>
      <View
        style={[
          {position: 'absolute', top: -10},
          side === 'left' ? {left: 10} : {right: 10},
        ]}>
        <Image source={image} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    width: '90%',
    display: 'flex',
    minHeight: 50,
    backgroundColor: '#FCF8E8',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginVertical: 2,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 12,
    borderColor: 'red',
    borderWidth: 1,
  },
  cardError: {
    backgroundColor: '#fc3030',
  },
  cardSuccess: {
    backgroundColor: '#27e844',
  },
  text: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#112164',
    textAlign: 'center',
    flexShrink: 1,
    width: '100%',
  },
});

export default CardAnswer;
