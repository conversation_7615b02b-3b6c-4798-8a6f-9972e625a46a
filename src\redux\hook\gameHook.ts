import {useDispatch} from 'react-redux';
import {setData, gameOver, restartGame, reset} from '../reducers/gameReducer';
import {getCurrentScore} from '../../modules/game/gameAsyncThunk';
import {CustomerActions} from '../reducers/CustomerReducer';

export const useGameHook = () => {
  const dispatch = useDispatch();

  const action = {
    setData: (data: any) => {
      dispatch(setData(data));
    },
    pauseGame: () => {
      dispatch(setData({stateName: 'isRunTime', value: false}));
    },
    continueGame: () => {
      dispatch(setData({stateName: 'isRunTime', value: true}));
    },
    resetGame: () => {
      dispatch(reset());
    },
    gameOver: (message: string) => {
      dispatch(gameOver(message));
    },
    restartGame: () => {
      dispatch(restartGame());
    },
    getCurrentScore: async (gameId: string) => {
      return dispatch(getCurrentScore(gameId) as any);
    },
    updateScore: async ({
      totalScore,
      gameId,
    }: {
      totalScore: number;
      gameId: string;
    }) => {
      await dispatch(CustomerActions.updateRank(totalScore, gameId) as any);
    },
  };

  return action;
};
