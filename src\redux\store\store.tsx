import {configureStore} from '@reduxjs/toolkit';
import ratingReducer from '../reducers/ratingReducer';
import courseReducer from '../reducers/courseReducer';
import customerReducer from '../reducers/CustomerReducer';
import newsFeedReducer from '../../modules/community/reducers/newsFeedReducer';
import MyFeedReducer from '../../modules/community/reducers/MyFeedReducer';
import examReducer from '../reducers/examReducer';
import proccessLessonReducer from '../reducers/proccessLessonReducer';
import groupReducer from '../../modules/community/reducers/groupReducer';
import followingGroupsReducer from '../../modules/community/reducers/followingGroupsReducer';
import confirmGroupsReducer from '../../modules/community/reducers/confirmGroupsReducer';
import myGroupsReducer from '../../modules/community/reducers/myGroupsReducer';
import notificationReducer from '../reducers/notificationReducer';
import postCommentsReducer from '../../modules/community/reducers/postCommentsReducer';
import groupPostsReducer from '../../modules/community/reducers/groupPostsReducer';
import ALTPReducer from '../../modules/game/ailatrieuphu/redux/gameSlice';
import SakuTBReducer from '../reducers/game/sakuTBReducer';
import gameReducer from '../reducers/gameReducer';
import SakuLCReducer from '../reducers/game/sakuLCReducer';
import SakuXTReducer from '../reducers/game/sakuXTReducer';
import MGHHReducer from '../../modules/game/manhghephoanhao/redux/MGHHReducer';
import dhbcReducer from '../reducers/game/dhbcReducer';
// import customerReducer from '../reducers/user/reducer'
import SakuSMReducer from '../reducers/game/sakuSMReducer';
import SakuTCReducer from '../../modules/game/sakuchuyencanh/redux/reducers/sakuTcReducer';
import VTNVReducer from '../../modules/game/vuotchuongngaivat/redux/reducers/VTNVReducer';

export const store = configureStore({
  reducer: {
    ratings: ratingReducer,
    course: courseReducer,
    customer: customerReducer,
    newsFeed: newsFeedReducer,
    exam: examReducer,
    process: proccessLessonReducer,
    group: groupReducer,
    followingGroups: followingGroupsReducer,
    myGroups: myGroupsReducer,
    notification: notificationReducer,
    postComments: postCommentsReducer,
    groupPosts: groupPostsReducer,
    myFeed: MyFeedReducer,
    game: ALTPReducer,
    confirmGroups: confirmGroupsReducer,
    SakuTB: SakuTBReducer,
    gameStore: gameReducer,
    SakuLC: SakuLCReducer,
    SakuXT: SakuXTReducer,
    MGHHStore: MGHHReducer,
    dhbcStore: dhbcReducer,
    SakuSMStore: SakuSMReducer,
    SakuTCStore: SakuTCReducer,
    VTNVStore: VTNVReducer,
  },
});

export default store;
export type AppDispatch = typeof store.dispatch;
export type RootState = ReturnType<typeof store.getState>;
