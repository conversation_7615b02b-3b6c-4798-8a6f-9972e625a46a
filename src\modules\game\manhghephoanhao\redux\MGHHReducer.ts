import {createSlice} from '@reduxjs/toolkit';
import {
  Question,
} from '../models/models';
import { loadGameConfig, loadGameQuestions } from './MGHHAsyncThunk';

interface State {
  dataListQuestion: Question[];
  listQuestions: Question[];
  currentQuestion: Question | null;
  currentLevel: number;
  totalQuestion: number;
  questionDone: number;
  bonusLv1: number;
  bonusLv2: number;
  bonusLv3: number;
  timeLimit: number;
}

const initialState: State = {
  dataListQuestion: [],
  listQuestions: [],
  currentQuestion: null,
  currentLevel: 0,
  totalQuestion: 0,
  questionDone: 0,
  bonusLv1: 0,
  bonusLv2: 0,
  bonusLv3: 0,
  timeLimit: 0,
};

export const MGHHReducer = createSlice({
  name: 'MGHHReducer',
  initialState,
  reducers: {
    setData(state, action) {
      state[action.payload.stateName] = action.payload.value;
    },
    startGame: state => {
      state.currentLevel = 1;
      const questionLevel = state.dataListQuestion.filter(
        question => question.level === state.currentLevel,
      );
      state.listQuestions = questionLevel;
      state.currentQuestion = questionLevel[0];
      state.totalQuestion = questionLevel.length;
      state.questionDone = 0;
    },
    nextQuestion: state => {
      state.currentQuestion = state.listQuestions[state.questionDone];
    },
    nextLevel: state => {
      const listQuestion = [...state.dataListQuestion];
      const currentLevel = state.currentLevel + 1;
      const questionLevel = listQuestion.filter(
        question => question.level === currentLevel,
      );
      state.currentLevel = currentLevel;
      state.listQuestions = questionLevel;
      state.currentQuestion = questionLevel[0];
      state.questionDone = 0;
      state.totalQuestion = questionLevel.length;
    },
    restartLevel: state => {
      state.currentQuestion = state.listQuestions[0];
      state.questionDone = 0;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(loadGameConfig.fulfilled, (state, action) => {
        state.bonusLv1 = action.payload.bonusLv1;
        state.bonusLv2 = action.payload.bonusLv2;
        state.bonusLv3 = action.payload.bonusLv3;
        state.timeLimit = action.payload.timeLimit;
    });
    builder.addCase(loadGameQuestions.fulfilled, (state, action) => {
      console.log('loadGameQuestions')
      state.dataListQuestion = action.payload.questions || [];
    });
  },
});

export const {
  setData,
  startGame,
  nextQuestion,
  nextLevel,
  restartLevel,
} = MGHHReducer.actions;

export default MGHHReducer.reducer;
