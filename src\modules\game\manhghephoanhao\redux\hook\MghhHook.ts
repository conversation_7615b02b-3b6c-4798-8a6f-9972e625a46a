import {useDispatch} from 'react-redux';
import { startGame, setData, nextQuestion, nextLevel, restartLevel} from '../MGHHReducer';
import { loadGameConfig, loadGameQuestions } from '../MGHHAsyncThunk';

export const useMghhHook = () => {
  const dispatch = useDispatch();

  const action = {
    setData: (data: any) => {
      dispatch(setData(data));
    },
    startGame: () => {
      dispatch(startGame());
    },
    nextQuestion: () => {
      dispatch(nextQuestion());
    },
    nextLevel: () => {
      dispatch(nextLevel());
    },
    restartLevel: () => {
      dispatch(restartLevel());
    },
    loadGameConfig: ({gameId}: {gameId: string}) => {
      dispatch(loadGameConfig({gameId}) as any);
    },
    loadGameQuestions: ({gameId, stage, competenceId}: {gameId: string, stage: number, competenceId: string}) => {
      dispatch(loadGameQuestions({gameId, stage, competenceId}) as any);
    },
  };

  return action;
};
