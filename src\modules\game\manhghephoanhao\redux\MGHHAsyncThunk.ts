import { createAsyncThunk } from "@reduxjs/toolkit";
import { GetGameConfigRequest } from "../../sakutimban/types/sakuTBTypes";
import { MghhDa } from "../da/MghhDa";
import { transformQuestions, validateTransformedQuestion } from "../../sakutimban/utils/sakuTBUtils";
import { initializeFallbackData } from "../../sakutimban/data/fallbackData";

// Async thunk để load GameConfig từ API
 const loadGameConfig = createAsyncThunk(
  'MGHHReducer/loadGameConfig',
  async ({ gameId }: GetGameConfigRequest) => {
    try {
      const gameConfig = await MghhDa.getGameConfig(gameId);
     if(gameConfig){
        return gameConfig;
      }
    } catch (error) {
      return {
          gameId: gameId,
          bonusLv1: 0,
          bonusLv2: 0,
          bonusLv3: 0,
          timeLimit: 450,
      };
    }
  }
);

// Async thunk để load questions từ API
const loadGameQuestions = createAsyncThunk(
  'MGHHReducer/loadQuestions',
  async ({ gameId, stage, competenceId }: { gameId: string; stage: number, competenceId: string }) => {
    try {
      const sakuTBDA = new MghhDa();
      const questions = await sakuTBDA.getQuestionsByGameAndStage(gameId, stage, competenceId);
      if (questions.length === 0) {
        return {
          questions: [],
        };
      }
      return {
        questions,
      };
    } catch (error) {
      console.error('[Redux] Error loading questions:', error);
      // Fallback to local data
      const fallbackData = initializeFallbackData();
      return {
        questions: [],
      }
    }
  }
);

export {loadGameConfig, loadGameQuestions};

