import {
  ImageBackground,
  StyleSheet,
  View,
  Text,
  ActivityIndicator,
  Alert,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import Lives from '../components/Lives';
import CountBadge from '../components/CountQuestions';
import {CardTitleGame} from '../components/CardTitleGame';
import {useSelector} from 'react-redux';
import store, {RootState} from '../../../redux/store/store';
import {BottomGame} from '../components/BottomGame';
import {useGameHook} from '../../../redux/hook/gameHook';
import {useSakuSMHook} from '../../../redux/hook/game/sakusmHook';
import ModelConfirm from '../components/ModelConfirm';
import HintModel from '../components/HintModel';
import GameOverModal from '../components/GameOverModel';
import {useEffect, useState, useCallback} from 'react';
import CardAnswer from './components/CardAnswer';
import React from 'react';
import ModelPauseGame from '../components/ModelPauseGame';
import ConfigAPI from '../../../Config/ConfigAPI';
import {useRoute, RouteProp, useNavigation} from '@react-navigation/native';
import {GameDA} from '../gameDA';
import {randomGID} from '../../../utils/Utils';
import {DataController} from '../../../base/baseController';
import WinnerModal from './components/WinnerModal';

// Type cho route params
type SakuSMRouteParams = {
  milestoneId?: number;
  competenceId?: string;
};

const StartSakuSM = () => {
  const route = useRoute<RouteProp<{params: SakuSMRouteParams}, 'params'>>();
  const gameHook = useGameHook();
  const sakuSMHook = useSakuSMHook();

  // Lấy params từ navigation
  const milestoneId = route.params?.milestoneId || 1;
  const competenceId = route.params?.competenceId || '';

  const {
    currentQuestion,
    questionDone,
    totalQuestion,
    listQuestion,
    loading,
    configLoading,
    error,
    configError,
    gameConfig,
    usedHints,
  } = useSelector((state: RootState) => state.SakuSMStore);

  const {isGameOver, messageGameOver, totalLives, currentLives, gem} =
    useSelector((state: RootState) => state.gameStore);

  const [showModelConfirm, setShowModelConfirm] = useState<boolean>(false);
  const [showHintModel, setShowHintModel] = useState<boolean>(false);
  const [isWinLevel, setIsWinLevel] = useState<boolean>(false);
  const [isAllowChoose, setIsAllowChoose] = useState<boolean>(true);
  const [isPauseGame, setIsPauseGame] = useState<boolean>(false);
  //navigation
  const navigation = useNavigation<any>();

  // Kiểm tra xem câu hỏi hiện tại có hint không và đã sử dụng chưa
  const hasHint = currentQuestion?.hint && currentQuestion.hint.trim() !== '';
  const isHintUsed = usedHints.includes(currentQuestion?.id?.toString() || '');
  const shouldShowHintButton = Boolean(hasHint && !isHintUsed);

  const resetData = useCallback(() => {
    setShowModelConfirm(false);
    setShowHintModel(false);
    setIsWinLevel(false);
    setIsAllowChoose(true);
  }, []);

  // Chuyển sang câu hỏi tiếp theo
  const nextQuestion = useCallback(() => {
    sakuSMHook.setData({stateName: 'questionDone', value: questionDone + 1});

    if (questionDone + 1 >= totalQuestion) {
      setIsWinLevel(true);
      return;
    }
    sakuSMHook.setData({
      stateName: 'currentQuestion',
      value: listQuestion[questionDone + 1],
    });
    resetData();
  }, [questionDone]);

  // Memoize onChooseAnswer để tránh re-render
  const onChooseAnswer = useCallback(
    (status: 'correct' | 'wrong' | null) => {
      if (!status) return;
      setIsAllowChoose(false);

      if (status === 'wrong') {
        // Chỉ trừ mạng, không next câu
        gameHook.setData({stateName: 'currentLives', value: currentLives - 1});
        setTimeout(() => {
          setIsAllowChoose(true); // Cho phép chọn lại
        }, 1000);
      } else if (status === 'correct') {
        // Chỉ next câu khi đáp án đúng
        setTimeout(() => {
          nextQuestion();
        }, 1000);
      }
    },
    [currentLives, gameHook, nextQuestion],
  );

  // Memoize answers component
  const answersComponent = React.useMemo(() => {
    return currentQuestion.answers.map((item, index) => (
      <View key={index}>
        <CardAnswer
          allowChoose={isAllowChoose}
          answer={item}
          index={index}
          side={index % 2 === 0 ? 'left' : 'right'}
          onClick={onChooseAnswer}
        />
      </View>
    ));
  }, [currentQuestion.answers, isAllowChoose, onChooseAnswer]);

  useEffect(() => {
    loadGameData();
    fetchScore();
  }, []);

  useEffect(() => {
    if (currentLives < 1) {
      onGameOver('Thất bại rồi, làm lại nào');
    }
  }, [currentLives]);

  // Load dữ liệu từ API
  const loadGameData = async () => {
    try {
      console.log('[SakuSM] Loading game data...');

      // Load config trước
      await sakuSMHook.loadGameConfig(ConfigAPI.gameSakusanmoi);

      // Load questions nếu có competenceId
      if (competenceId) {
        await sakuSMHook.loadQuestions(
          ConfigAPI.gameSakusanmoi,
          milestoneId,
          competenceId,
        );
      }

      // Start game sau khi load xong
      onStartGame();
    } catch (error) {
      console.error('[SakuSM] Failed to load game data:', error);
      // Nếu lỗi, vẫn có thể chơi với dữ liệu hardcode
      onStartGame();
    }
  };
  const fetchScore = async () => {
    try {
      // Lấy thông tin điểm từ bảng GameCUstomer
      const gameDa = new GameDA();
      const result = await gameDa.getScoreByCustomerIdAndGameId(
        store.getState().customer.data.Id,
        ConfigAPI.gameSakusanmoi,
      );
      gameHook.setData({stateName: 'gem', value: result ?? 0});
    } catch (error) {
      console.error('Lỗi khi lấy thông tin điểm:', error);
    }
  };
  const onStartGame = () => {
    resetData();
    sakuSMHook.startGame();

    // Sử dụng config từ API nếu có, nếu không thì dùng default
    const lives = gameConfig?.maxLives || 3;
    const time = gameConfig?.timeLimit || 300;

    gameHook.restartGame();
    gameHook.setData({stateName: 'totalLives', value: lives});
    gameHook.setData({stateName: 'currentLives', value: lives});
    gameHook.setData({stateName: 'time', value: time});
    gameHook.setData({stateName: 'isRunTime', value: true});
    gameHook.setData({stateName: 'gemCost', value: gameConfig?.gemHint || 10});
  };

  //Tạm dừng
  const onPauseGame = () => {
    setIsPauseGame(true);
    gameHook.pauseGame();
  };

  // Tiếp tục
  const onContinueGame = () => {
    setIsPauseGame(false);
    gameHook.continueGame();
  };

  // Thua game
  const onGameOver = (message: string) => {
    gameHook.gameOver(message);
  };

  // show model xác nhận dùng gợi ý
  const showModalConfirm = () => {
    setShowModelConfirm(true);
  };

  // show model gợi ý
  const onUseHint = () => {
    if (gem < (gameConfig?.gemHint || 10)) {
      // show model thông báo không đủ gem
      Alert.alert(
        'Thông báo',
        'Bạn không đủ gem để sử dụng gợi ý',
        [
          {
            text: 'OK',
            style: 'cancel',
          },
        ],
        {cancelable: false},
      );
      return;
    }
    // trừ gem
    gameHook.setData({
      stateName: 'gem',
      value: gem - (gameConfig?.gemHint || 10),
    });
    // trừ điểm trong data base
    updateScore((gameConfig?.gemHint || 10));
  };
  //tạo hàm update score vào data base
  const updateScore = async (score: number) => {
    if(score <= 0) return;
    const gamecustomerController = new DataController('GameCustomer');
    const customerId = store.getState().customer.data.Id;
    const game = {
      Id: randomGID(),
      CustomerId: customerId,
      GameId: ConfigAPI.gameSakusanmoi,
      Stage: milestoneId,
      Competency: competenceId,
      Status: 0,
      DateCreated: new Date().getTime(),
      Score: (-(gameConfig?.gemHint || 0)),
      HighestScore: 0,
      PlayedAt: new Date().getTime(),
      Name: `Sử dụng gợi ý - Sakusanmoi_${milestoneId}`,
    };

    console.log('Tạo bản ghi mới:', game);
    const result = await gamecustomerController.add([game]);
    if (result.code !== 200) {
      return false;
    }
    sakuSMHook.markHintUsed(currentQuestion.id.toString());
    setShowHintModel(true);
    return true;
  };

  // Kiểm tra xem có hiển thị loading không
  if (loading || configLoading) {
    return (
      <SafeAreaView
        style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={{marginTop: 16}}>Đang tải dữ liệu game...</Text>
      </SafeAreaView>
    );
  }

  // Kiểm tra xem có lỗi không
  if (error || configError) {
    return (
      <SafeAreaView
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          padding: 20,
        }}>
        <Text style={{fontSize: 18, textAlign: 'center', color: '#ff0000'}}>
          Chưa có dữ liệu câu hỏi cho game này
        </Text>
        <Text style={{marginTop: 10, textAlign: 'center', color: '#666'}}>
          {error || configError}
        </Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{flex: 1}}>
      <ImageBackground
        source={require('./assets/background.png')}
        style={{flex: 1}}>
        <View style={{marginHorizontal: 16, flex: 1}}>
          {/* Header */}
          <View>
            <HeadGame
              timeOut={() => onGameOver('Hết giờ rồi, làm lại nào')}
              onUseHint={showModalConfirm}
              isShowSuggest={shouldShowHintButton}
              gameId={ConfigAPI.gameSakusanmoi}
            />
            <View>
              <LineProgressBar
                progress={
                  (questionDone / totalQuestion) * 100
                }></LineProgressBar>
              <View
                style={{
                  width: '100%',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                <Lives
                  totalLives={totalLives}
                  currentLives={currentLives}></Lives>
                <CountBadge
                  current={questionDone}
                  total={totalQuestion}></CountBadge>
              </View>
            </View>
          </View>

          {/* Body */}
          <View style={{marginVertical: 16}}>
            <CardTitleGame
              title={currentQuestion.question || ''}></CardTitleGame>
            <View style={{marginTop: 100}}>{answersComponent}</View>
          </View>

          {/* Bottom */}
          <View style={{position: 'absolute', bottom: 0, left: 0}}>
            <BottomGame
              resetGame={onStartGame}
              backGame={() => {
                navigation.goBack();
              }}
              pauseGame={onPauseGame}
              volumeGame={() => {}}
            />
          </View>
        </View>
      </ImageBackground>

      {/* Modal */}
      <View style={{zIndex: 1000}}>
        <ModelConfirm
          isShow={showModelConfirm}
          closeModal={() => setShowModelConfirm(false)}
          onConfirm={onUseHint}
        />
        <HintModel
          isShow={showHintModel}
          closeModal={() => setShowHintModel(false)}
          text={currentQuestion.hint}
        />
        <ModelPauseGame
          visible={isPauseGame}
          message={'Bạn đang tạm dừng trò chơi'}
          onContinue={onContinueGame}
        />
        <GameOverModal
          visible={isGameOver}
          onClose={() => {}}
          restartGame={onStartGame}
          message={messageGameOver}
          isTimeOut={false}
        />
        <WinnerModal
          visible={isWinLevel}
          onClose={() => {
            setIsWinLevel(false);
          }}
          restartGame={onStartGame}
          currentLives={currentLives}
          competenceId={competenceId}
          gameId={ConfigAPI.gameSakusanmoi}
        />
      </View>
    </SafeAreaView>
  );
};
export default StartSakuSM;
