import React, {useEffect, useState, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  BackHandler,
  Alert,
  Dimensions,
  Image,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import {Winicon} from 'wini-mobile-components';
// import Sound from 'react-native-sound'; // Sẽ import bằng tay

import {AppDispatch, RootState} from '../../../redux/store/store';
import {
  // initializeGame,
  selectAnswer,
  nextQuestion,
  applyLifeline,
  takeMoneyAndQuit,
  setHighestScore,
  initializeGame,
  setCurrentMilestoneId,
  fetchQuestions,
  fetchMoneyLevels,
  resetGame,
  clearQuestions,
} from './redux/gameSlice';

import MoneyLadder from './components/MoneyLadder';
import AudienceHelp from './components/AudienceHelp';
import ExpertHelp from './components/ExpertHelp';
import GameOverModal from './components/GameOverModal';
import WinnerModal from './components/WinnerModal';
import MoneyLevelModal from './components/MoneyLevelModal';
import {formatMoney} from './data/questionSets';
import {
  getDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../../../utils/AsyncStorage';
import ConfigAPI from '../../../Config/ConfigAPI';

const {width} = Dimensions.get('window');

// Tạo key cho AsyncStorage
const HIGHEST_SCORE_KEY = 'ALTP_HIGHEST_SCORE';

const StartALTP = () => {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const dispatch: AppDispatch = useDispatch();

  const gameState = useSelector((state: RootState) => state.game);

  // Lấy milestone ID từ route params
  const {milestoneId, competenceId} = route.params || {milestoneId: 1};

  //tạo hook để reset
  const restartGame = useCallback(() => {
    setShowGameOverModal(false);

    // Clear questions trước khi reset game
    dispatch(clearQuestions());

    // Reset game state
    dispatch(resetGame());

    // Đặt milestone hiện tại
    dispatch(setCurrentMilestoneId(milestoneId));

    // Lấy câu hỏi mới từ API
    dispatch(fetchQuestions(milestoneId, competenceId));

    // Reset các state liên quan đến trợ giúp và thời gian
    setTimeLeft(60);
    setIsAnswering(false);
    setShowAudienceHelp(false);
    setShowExpertHelp(false);
    setShowPhoneCallHelp(false);
    setshouldBlink(false);

    // Cập nhật questionUpdateKey để force re-render câu hỏi
    setQuestionUpdateKey(prev => prev + 1);
  }, [dispatch, milestoneId, competenceId]);

  // Refs cho âm thanh
  // const correctSoundRef = useRef<Sound | null>(null);
  // const wrongSoundRef = useRef<Sound | null>(null);
  // const thinkingSoundRef = useRef<Sound | null>(null);
  // const winSoundRef = useRef<Sound | null>(null);

  // State cho modal
  const [showAudienceHelp, setShowAudienceHelp] = useState(false);
  const [showExpertHelp, setShowExpertHelp] = useState(false);
  const [showPhoneCallHelp, setShowPhoneCallHelp] = useState(false);
  const [isAnswering, setIsAnswering] = useState(false);
  const [showMoneyLadder, setShowMoneyLadder] = useState(false);
  const [showGameOverModal, setShowGameOverModal] = useState(false);
  const [showWinnerModal, setShowWinnerModal] = useState(false);
  const [showMoneyLevelModal, setShowMoneyLevelModal] = useState(false);
  const [gameOverMessage, setGameOverMessage] = useState('');
  const [isTimeOut, setIsTimeOut] = useState(false);
  const AnimTouchable = Animatable.createAnimatableComponent(TouchableOpacity);
  const [shouldBlink, setshouldBlink] = useState(false);
  // const shouldBlink = isCorrect || showCorrectAnswer;   // nhấp nháy khi đáp án này là đáp án đúng
  const ButtonComponent = shouldBlink ? AnimTouchable : TouchableOpacity;
  // State cho đếm ngược thời gian
  const [timeLeft, setTimeLeft] = useState(60);

  // State để theo dõi việc cập nhật câu hỏi
  const [questionUpdateKey, setQuestionUpdateKey] = useState(0);

  // Ref để đảm bảo chỉ khởi tạo game một lần
  const isInitialized = React.useRef(false);

  // Lấy câu hỏi hiện tại bằng useMemo để cập nhật khi currentQuestionIndex thay đổi
  const currentQuestion = React.useMemo(() => {
    console.log(
      'Cập nhật currentQuestion, index =',
      gameState.currentQuestionIndex,
    );
    console.log(
      'Câu hỏi hiện tại:',
      gameState.questions[gameState.currentQuestionIndex]?.question,
    );
    console.log('Tổng số câu hỏi:', gameState.questions?.length);
    console.log('questionUpdateKey:', questionUpdateKey);

    // Đảm bảo rằng chúng ta có câu hỏi và index hợp lệ
    if (
      gameState.questions &&
      gameState.currentQuestionIndex >= 0 &&
      gameState.currentQuestionIndex < gameState.questions.length
    ) {
      // Tạo một bản sao của câu hỏi để tránh tham chiếu đến cùng một đối tượng
      const question = {
        ...gameState.questions[gameState.currentQuestionIndex],
        // Thêm ID duy nhất cho câu hỏi để đảm bảo React nhận ra sự thay đổi
        uniqueId: `${gameState.currentQuestionIndex}-${questionUpdateKey}`,
      };

      return question;
    }
    return null;
  }, [gameState.questions, gameState.currentQuestionIndex, questionUpdateKey]);

  // Log mỗi khi component re-render
  console.log(
    'Component re-render, currentQuestionIndex =',
    gameState.currentQuestionIndex,
  );

  // Theo dõi sự thay đổi của currentQuestionIndex
  useEffect(() => {
    console.log(
      'useEffect: currentQuestionIndex đã thay đổi thành',
      gameState.currentQuestionIndex,
    );
    console.log(
      'useEffect: Câu hỏi hiện tại:',
      gameState.questions[gameState.currentQuestionIndex]?.question,
    );

    // Force re-render khi currentQuestionIndex thay đổi
    setQuestionUpdateKey(prev => prev + 1);

    // Lưu trữ index hiện tại để so sánh sau này
    const currentIndex = gameState.currentQuestionIndex;

    // Kiểm tra sau một khoảng thời gian để đảm bảo index đã được cập nhật
    const timer = setTimeout(() => {
      if (gameState.currentQuestionIndex !== currentIndex) {
        console.log(
          'Index đã thay đổi sau timeout:',
          gameState.currentQuestionIndex,
        );
      } else {
        console.log('Index không thay đổi sau timeout, vẫn là:', currentIndex);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [gameState.currentQuestionIndex, gameState.questions]);

  // Xác nhận thoát game
  const confirmQuit = useCallback(() => {
    Alert.alert(
      'Xác nhận',
      `Bạn có muốn dừng cuộc chơi và nhận ${formatMoney(
        gameState.currentMoney,
      )}?`,
      [
        {
          text: 'Không',
          style: 'cancel',
        },
        {
          text: 'Có',
          onPress: () => {
            dispatch(takeMoneyAndQuit());

            // Kiểm tra số câu đã trả lời để quyết định hiển thị modal nào
            if (gameState.currentQuestionIndex < 4) {
              // Nếu chưa qua câu hỏi thứ 5, hiển thị modal gameover
              setGameOverMessage('Bạn đã dừng cuộc chơi!');
              setIsTimeOut(false);
              setShowGameOverModal(true);
            } else {
              // Nếu đã qua câu hỏi thứ 5, hiển thị modal win
              setIsTimeOut(false);
              setShowWinnerModal(true);
            }
          },
        },
      ],
      {cancelable: false},
    );
  }, [
    dispatch,
    gameState.currentMoney,
    gameState.currentQuestionIndex,
    setGameOverMessage,
    setIsTimeOut,
    setShowGameOverModal,
    setShowWinnerModal,
  ]);

  // Khởi tạo game khi component mount - chỉ chạy một lần
  useEffect(() => {
    console.log('Component mounted - Initializing game');

    if (!isInitialized.current) {
      console.log('Khởi tạo game lần đầu');

      // Đặt milestone hiện tại
      dispatch(setCurrentMilestoneId(milestoneId));

      // Lấy moneyLevels từ API
      dispatch(fetchMoneyLevels(ConfigAPI.gameALTP));

      // Lấy câu hỏi từ API
      dispatch(fetchQuestions(milestoneId, competenceId));

      // Khởi tạo game với bộ câu hỏi mặc định (sẽ được thay thế khi API trả về kết quả)
      dispatch(initializeGame('general'));

      isInitialized.current = true;
    } else {
      console.log('Game đã được khởi tạo, không khởi tạo lại');
    }

    // Lấy điểm cao nhất từ AsyncStorage
    const loadHighestScore = async () => {
      try {
        const scoreStr = await getDataToAsyncStorage(HIGHEST_SCORE_KEY);
        if (scoreStr) {
          const score = parseInt(scoreStr, 10);
          if (!isNaN(score)) {
            dispatch(setHighestScore(score));
          }
        }
      } catch (error) {
        console.error('Error loading highest score:', error);
      }
    };

    loadHighestScore();

    // Khởi tạo âm thanh
    // Sound.setCategory('Playback');

    // correctSoundRef.current = new Sound(
    //   'correct_answer.mp3',
    //   Sound.MAIN_BUNDLE,
    //   error => {
    //     if (error) console.error('Error loading correct sound:', error);
    //   }
    // );

    // wrongSoundRef.current = new Sound(
    //   'wrong_answer.mp3',
    //   Sound.MAIN_BUNDLE,
    //   error => {
    //     if (error) console.error('Error loading wrong sound:', error);
    //   }
    // );

    // thinkingSoundRef.current = new Sound(
    //   'thinking.mp3',
    //   Sound.MAIN_BUNDLE,
    //   error => {
    //     if (error) console.error('Error loading thinking sound:', error);
    //   }
    // );

    // winSoundRef.current = new Sound(
    //   'win.mp3',
    //   Sound.MAIN_BUNDLE,
    //   error => {
    //     if (error) console.error('Error loading win sound:', error);
    //   }
    // );

    // Xử lý nút back
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      () => {
        if (!isAnswering && !gameState.isGameOver) {
          // Sử dụng hàm confirmQuit đã cập nhật để hiển thị modal phù hợp
          confirmQuit();
          return true;
        }
        return false;
      },
    );

    return () => {
      backHandler.remove();

      // Giải phóng âm thanh
      // correctSoundRef.current?.release();
      // wrongSoundRef.current?.release();
      // thinkingSoundRef.current?.release();
      // winSoundRef.current?.release();
    };
  }, [dispatch, milestoneId, confirmQuit, isAnswering, gameState.isGameOver, competenceId]); // Thêm dependencies cần thiết

  // Đếm ngược thời gian
  useEffect(() => {
    if (isAnswering || gameState.isGameOver) {
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          // Hết thời gian, xử lý thua cuộc
          dispatch(takeMoneyAndQuit());

          // Sử dụng setTimeout để đảm bảo setState không xảy ra trong quá trình render
          setTimeout(() => {
            // Kiểm tra nếu chưa qua câu hỏi thứ 5, hiển thị modal thay vì chuyển màn hình
            if (gameState.currentQuestionIndex < 4) {
              setIsTimeOut(true);
              setGameOverMessage('Hết giờ rồi, làm lại nào');
              setShowGameOverModal(true);
            } else {
              // Nếu đã qua câu hỏi thứ 5, hiển thị modal chiến thắng
              setIsTimeOut(true);
              setShowWinnerModal(true);
            }
          }, 0);

          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [
    gameState.currentQuestionIndex,
    isAnswering,
    gameState.isGameOver,
    dispatch,
    navigation,
  ]);

  // Lưu điểm cao nhất khi game kết thúc
  useEffect(() => {
    if (gameState.isGameOver) {
      const saveHighestScore = async () => {
        try {
          // await getDataToAsyncStorage(HIGHEST_SCORE_KEY, gameState.highestScore.toString());
          await saveDataToAsyncStorage(
            HIGHEST_SCORE_KEY,
            gameState.highestScore.toString(),
          );
        } catch (error) {
          console.error('Error saving highest score:', error);
        }
      };

      saveHighestScore();
    }
  }, [gameState.isGameOver, gameState.highestScore]);

  // State để theo dõi đáp án đã chọn trước khi cập nhật Redux
  const [localSelectedAnswer, setLocalSelectedAnswer] = useState<number | null>(
    null,
  );

  // Xử lý khi chọn đáp án
  const handleSelectAnswer = (index: number) => {
    console.log('Đáp án được chọn:', index);
    console.log(
      'Current question index before answer:',
      gameState.currentQuestionIndex,
    );

    if (isAnswering) {
      console.log('Đang trong quá trình trả lời, không thể chọn đáp án khác');
      return;
    }

    // Kiểm tra nếu currentQuestion là null
    if (!currentQuestion) {
      console.error('Không có câu hỏi hiện tại!');
      return;
    }

    // Đặt trạng thái đang trả lời
    setIsAnswering(true);
    console.log('Đã đặt isAnswering = true');

    // Tạo biến tạm để lưu trữ đáp án đã chọn
    const selectedAnswerIndex = index;
    const isCorrect =
      selectedAnswerIndex === currentQuestion.correctAnswerIndex;

    // Giai đoạn 1: Cập nhật state local để hiển thị đáp án đã chọn
    setLocalSelectedAnswer(selectedAnswerIndex);
    console.log('Đã cập nhật localSelectedAnswer:', selectedAnswerIndex);

    // Phát âm thanh suy nghĩ
    // thinkingSoundRef.current?.play();

    // Giai đoạn 2: Hiển thị kết quả đúng/sai sau 1.5 giây
    setTimeout(() => {
      // Dừng âm thanh suy nghĩ
      // thinkingSoundRef.current?.stop();

      // Dispatch action để cập nhật state trong Redux
      dispatch(selectAnswer(selectedAnswerIndex));
      setshouldBlink(true);
      console.log(
        'Đã dispatch action selectAnswer với index:',
        selectedAnswerIndex,
      );
      console.log(
        'Current question index after selectAnswer:',
        gameState.currentQuestionIndex,
      );

      // Phát âm thanh đúng/sai
      if (isCorrect) {
        // correctSoundRef.current?.play();
        console.log('Đáp án đúng!');
      } else {
        // wrongSoundRef.current?.play();
        console.log('Đáp án sai!');
      }
    }, 500);
    // Giai đoạn 3: Chuyển câu hỏi hoặc kết thúc sau 3 giây nữa
    // Thời gian dài hơn để người dùng có thể thấy đáp án đúng/sai
    setTimeout(() => {
      console.log(
        'Current question index before navigation/next:',
        gameState.currentQuestionIndex,
      );

      if (isCorrect) {
        // Nếu đây là câu hỏi cuối cùng
        if (gameState.currentQuestionIndex === gameState.questions.length - 1) {
          console.log('Đã hoàn thành tất cả câu hỏi!');
          // winSoundRef.current?.play();
          // Hiển thị modal chiến thắng thay vì chuyển đến màn hình kết quả
          setIsTimeOut(false);
          setShowWinnerModal(true);
        } else {
          // Chuyển sang câu hỏi tiếp theo
          console.log('Chuyển sang câu hỏi tiếp theo');

          // Lưu trữ index hiện tại để debug
          const currentIndex = gameState.currentQuestionIndex;

          // Lưu trữ câu hỏi hiện tại để debug
          const currentQuestionText =
            gameState.questions[currentIndex]?.question;
          const nextQuestionText =
            gameState.questions[currentIndex + 1]?.question;

          console.log('Câu hỏi hiện tại:', currentQuestionText);
          console.log('Câu hỏi tiếp theo (dự kiến):', nextQuestionText);

          // Dispatch action để chuyển câu hỏi
          dispatch(nextQuestion());
          setshouldBlink(false); // 200 ms / lần
          // Sử dụng Promise để đảm bảo state đã được cập nhật
          Promise.resolve().then(() => {
            // Thêm log để debug sau khi state đã được cập nhật
            console.log(
              'Current question index after nextQuestion:',
              gameState.currentQuestionIndex,
            );

            // Sử dụng setTimeout để đảm bảo UI đã được cập nhật
            setTimeout(() => {
              // Reset các state
              setIsAnswering(false);
              setTimeLeft(60); // Reset thời gian
              setLocalSelectedAnswer(null); // Reset local state

              // Force re-render component bằng cách cập nhật questionUpdateKey
              setQuestionUpdateKey(prev => prev + 1);
              console.log('Reset state và force re-render');

              // Thêm log để kiểm tra câu hỏi sau khi re-render
              console.log(
                'Câu hỏi sau khi re-render:',
                gameState.questions[gameState.currentQuestionIndex]?.question,
              );
            }, 200);
          });
        }
      } else {
        // Sử dụng setTimeout để đảm bảo setState không xảy ra trong quá trình render
        setTimeout(() => {
          // Kiểm tra nếu chưa qua câu hỏi thứ 5, hiển thị modal thay vì chuyển màn hình
          if (gameState.currentQuestionIndex < 4) {
            console.log('Hiển thị modal thông báo thua cuộc');
            setIsTimeOut(false);
            setGameOverMessage('Rất tiếc! Bạn đã trả lời sai.');
            setShowGameOverModal(true);
          } else {
            // Nếu đã qua câu hỏi thứ 5, hiển thị modal chiến thắng
            console.log('Hiển thị modal chiến thắng');
            setIsTimeOut(false);
            setShowWinnerModal(true);
          }
        }, 0);
      }
    }, 2000); // Tăng thời gian lên 3 giây để người dùng có thể thấy đáp án đúng
  };

  // Xử lý khi sử dụng quyền trợ giúp
  const handleUseLifeline = (lifeline: string) => {
    dispatch(applyLifeline(lifeline));

    // Hiển thị modal tương ứng
    if (lifeline === 'audience') {
      setShowAudienceHelp(true);
    } else if (lifeline === 'expert') {
      setShowExpertHelp(true);
    } else if (lifeline === 'phoneCall') {
      setShowPhoneCallHelp(true);
    }
  };

  // Hiển thị loading khi đang lấy câu hỏi từ API
  if (gameState.isLoadingQuestions) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Đang tải câu hỏi từ API...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Hiển thị lỗi nếu có
  if (gameState.questionError) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.errorText}>Lỗi: {gameState.questionError}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => dispatch(fetchQuestions(milestoneId, competenceId))}>
            <Text style={styles.retryButtonText}>Thử lại</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Nếu chưa có câu hỏi, hiển thị màn hình loading
  if (!currentQuestion) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Đang tải câu hỏi...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Tính toán phần trăm tiến độ
  const calculateProgress = () => {
    return `${
      (gameState.currentQuestionIndex / gameState.questions.length) * 100
    }%`;
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.gameContainer}>
        {/* Header - Top Toolbar */}
        <View style={styles.header}>
          {/* Back Button */}
          <TouchableOpacity onPress={confirmQuit} style={styles.iconBack}>
            {/* <Image
              source={require('./assets/back-button.png')}
              style={styles.iconImage}
              // Thay bằng icon thực tế
            /> */}
            <Winicon src="fill/arrows/arrow-left-2" size={35} color="#fff" />
          </TouchableOpacity>

          {/* Score */}
          <TouchableOpacity
            style={styles.scoreContainer}
            onPress={() => setShowMoneyLevelModal(true)}>
            <Image
              source={require('./assets/coin-icon.png')}
              style={styles.scoreIcon}
            />
            <Text style={styles.scoreText}>{gameState.currentMoney}</Text>
          </TouchableOpacity>

          {/* Rank Container */}
          <TouchableOpacity
            style={styles.rankContainer}
            onPress={() => navigation.navigate('GameRanking', {gameId: ConfigAPI.gameALTP})}>
            <Image
              source={require('./assets/rank.png')}
              style={styles.rankIcon}
              // Thay bằng icon thực tế
            />
            <Text style={styles.rankText}>Xếp hạng</Text>
          </TouchableOpacity>

          {/* Sound Button */}
          <TouchableOpacity>
            <Image
              source={require('./assets/sound-icon.png')}
              style={styles.iconImage}
              // Thay bằng icon thực tế
            />
          </TouchableOpacity>
        </View>

        {/* Progress Bar */}
        <View style={styles.progressBar}>
          <View
            style={[styles.progressFill, {width: calculateProgress()} as any]}
          />
        </View>

        {/* Stats Container */}
        <View style={styles.statsContainer}>
          {/* Timer */}
          <View style={styles.statBox}>
            <Text style={styles.statText}>{timeLeft}s</Text>
          </View>

          {/* Question Counter */}
          <View style={styles.statBox}>
            <Text style={styles.statText}>
              {gameState.currentQuestionIndex + 1}/{gameState.questions.length}
            </Text>
          </View>
        </View>

        {/* Question Card */}
        <View style={styles.questionCard}>
          <Text style={styles.questionText}>
            {currentQuestion ? currentQuestion.question : 'Đang tải câu hỏi...'}
          </Text>
          <Text style={styles.questionIndexText}>
            Câu hỏi {gameState.currentQuestionIndex + 1}
          </Text>
        </View>

        {/* Answer Options Container */}
        <View style={styles.answerOptionsContainer}>
          {/* Answer Options */}
          {currentQuestion && currentQuestion.options
            ? currentQuestion.options.map((option, index) => {
                // Xác định trạng thái của đáp án
                const isLocalSelected = localSelectedAnswer === index;
                const isReduxSelected = gameState.selectedAnswer === index;
                const isCorrect =
                  gameState.isAnswerCorrect === true &&
                  index === currentQuestion.correctAnswerIndex;
                const isWrong =
                  gameState.isAnswerCorrect === false &&
                  gameState.selectedAnswer === index;

                // Hiển thị đáp án đúng khi người chơi trả lời sai
                const showCorrectAnswer =
                  gameState.isAnswerCorrect === false &&
                  index === currentQuestion.correctAnswerIndex;

                // Xác định style cho đáp án
                const buttonStyle = [
                  styles.answerButton,
                  // Hiển thị đáp án đã chọn khi chưa có kết quả
                  isLocalSelected &&
                    gameState.isAnswerCorrect === null &&
                    styles.selectedAnswerButton,
                  // Hiển thị đáp án đúng hoặc đáp án đúng khi trả lời sai
                  (isCorrect || showCorrectAnswer) &&
                    styles.correctAnswerButton,
                  // Hiển thị đáp án sai
                  isWrong && styles.wrongAnswerButton,
                  // Hiển thị đáp án đã bị loại bỏ
                  gameState.eliminatedOptions.includes(index) &&
                    styles.eliminatedOption,
                ];

                // Xác định style cho text
                const textStyle = [
                  styles.answerText,
                  (isLocalSelected ||
                    isReduxSelected ||
                    isCorrect ||
                    showCorrectAnswer) &&
                    styles.selectedAnswerText,
                ];

                console.log(
                  `Đáp án ${index}: isLocalSelected=${isLocalSelected}, isReduxSelected=${isReduxSelected}, isCorrect=${isCorrect}, isWrong=${isWrong}, showCorrectAnswer=${showCorrectAnswer}`,
                );

                return (
                  <ButtonComponent
                    key={`answer-${index}-${questionUpdateKey}`}
                    style={buttonStyle}
                    onPress={() => handleSelectAnswer(index)}
                    disabled={
                      isAnswering ||
                      gameState.isGameOver ||
                      gameState.eliminatedOptions.includes(index)
                    }
                    animation={
                      shouldBlink && (isCorrect || showCorrectAnswer)
                        ? 'flash'
                        : undefined
                    } // ‘flash’ có sẵn trong Animatable
                    iterationCount={
                      shouldBlink && (isCorrect || showCorrectAnswer) ? 1 : 1
                    } // nhấp nháy 6 lần (≈1.2 s)
                    duration={600} // 200 ms / lần
                  >
                    <Text style={textStyle}>{option}</Text>
                  </ButtonComponent>
                );
              })
            : // Hiển thị placeholder khi chưa có câu hỏi
              Array(4)
                .fill(0)
                .map((_, index) => (
                  <View
                    key={`placeholder-${index}`}
                    style={[styles.answerButton, styles.placeholderButton]}>
                    <Text style={styles.placeholderText}>
                      Đang tải đáp án...
                    </Text>
                  </View>
                ))}
        </View>

        {/* Bottom Toolbar */}
        <View style={styles.bottomToolbar}>
          {/* Phone Call Button */}
          <TouchableOpacity
            style={[
              styles.toolbarButton,
              !gameState.availableLifelines.audience &&
                styles.disabledToolbarButton,
            ]}
            onPress={() => handleUseLifeline('audience')}
            disabled={!gameState.availableLifelines.audience || isAnswering}>
            <Winicon src="fill/business/bulb-62" size={24} color="#fff" />
          </TouchableOpacity>

          {/* 50:50 Button */}
          <TouchableOpacity
            style={[
              styles.toolbarButton,
              !gameState.availableLifelines.fiftyFifty &&
                styles.disabledToolbarButton,
            ]}
            onPress={() => handleUseLifeline('fiftyFifty')}
            disabled={!gameState.availableLifelines.fiftyFifty || isAnswering}>
            <Text style={styles.toolbarButtonText}>50:50</Text>
          </TouchableOpacity>

          {/* Audience Button */}
          <TouchableOpacity
            style={[
              styles.toolbarButton,
              !gameState.availableLifelines.phoneCall &&
                styles.disabledToolbarButton,
            ]}
            onPress={() => handleUseLifeline('phoneCall')}
            disabled={!gameState.availableLifelines.phoneCall || isAnswering}>
            {/* <Image
              source={require('./assets/phone-call.png')}
              style={styles.iconImage}
              // Thay bằng icon thực tế
            /> */}
            {/* <Winicon src="color/sport/users-mm" size={24} color="#fff" /> */}
            <Winicon
              src="fill/user interface/phone-call"
              size={24}
              color="#fff"
            />
          </TouchableOpacity>

          {/* Expert Button */}
          <TouchableOpacity
            style={[
              styles.toolbarButton,
              !gameState.availableLifelines.expert &&
                styles.disabledToolbarButton,
            ]}
            onPress={() => handleUseLifeline('expert')}
            disabled={!gameState.availableLifelines.expert || isAnswering}>
            {/* <Image
              source={require('./assets/chuyengia.png')}
              style={styles.iconImage}
              // Thay bằng icon thực tế
            /> */}
            {/* <Text style={styles.toolbarButtonText}>🎓</Text> */}
            <Winicon src="fill/education/hat-3" size={24} color="#fff" />
          </TouchableOpacity>
        </View>

        {/* Money Ladder (Slide in from right) */}
        {showMoneyLadder && (
          <Animatable.View
            animation="slideInRight"
            duration={300}
            style={styles.moneyLadderContainer}>
            <MoneyLadder
              currentLevel={gameState.currentQuestionIndex}
              guaranteedLevel={gameState.guaranteedMoney}
            />
            <TouchableOpacity
              style={styles.closeLadderButton}
              onPress={() => setShowMoneyLadder(false)}>
              <Text style={styles.closeLadderButtonText}>×</Text>
            </TouchableOpacity>
          </Animatable.View>
        )}
      </View>

      {/* Audience Help Modal */}
      {gameState.audiencePercentages && (
        <AudienceHelp
          visible={showAudienceHelp}
          onClose={() => setShowAudienceHelp(false)}
          percentages={gameState.audiencePercentages}
        />
      )}

      {/* Expert Help Modal */}
      {gameState.expertAnswer !== null && (
        <ExpertHelp
          visible={showExpertHelp}
          onClose={() => setShowExpertHelp(false)}
          answerIndex={gameState.expertAnswer}
        />
      )}

      {/* Phone Call Help Modal */}
      {gameState.phoneCallAnswer !== null && (
        <ExpertHelp
          visible={showPhoneCallHelp}
          onClose={() => setShowPhoneCallHelp(false)}
          answerIndex={gameState.phoneCallAnswer}
          isPhoneCall={true}
        />
      )}

      {/* Game Over Modal */}
      <GameOverModal
        visible={showGameOverModal}
        onClose={() => setShowGameOverModal(false)}
        restartGame={restartGame}
        message={gameOverMessage}
        isTimeOut={isTimeOut}
      />

      {/* Winner Modal */}
      <WinnerModal
        visible={showWinnerModal}
        onClose={() => {
          setShowWinnerModal(false);
        }}
        gameId={ConfigAPI.gameALTP}
        competenceId={competenceId}
        // score={gameState.currentMoney}
        isTimeOut={isTimeOut}
      />

      {/* Money Level Modal */}
      <MoneyLevelModal
        visible={showMoneyLevelModal}
        onClose={() => setShowMoneyLevelModal(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  iconImage: {
    width: 50,
    height: 50,
  },
  iconBack: {
    width: 50,
    height: 50,
    borderRadius: 40,
    backgroundColor: '#FF9A0B',
    justifyContent: 'center',
    alignItems: 'center',
  },
  eliminatedOption: {
    opacity: 0.5,
  },
  container: {
    flex: 1,
    backgroundColor: '#F1D1A6', // Màu nền beige như trong hình
  },
  answerOptionsContainer: {
    paddingHorizontal: 10, // Thêm padding cho container đáp án
    marginBottom: 10,
  },
  gameContainer: {
    flex: 1,
    flexDirection: 'column',
    padding: 16,
    paddingVertical: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FF5757', // Màu đỏ cam như trong hình
    paddingVertical: 4,
    paddingHorizontal: 4,
    borderRadius: 20,
    marginBottom: 10,
  },
  progressBar: {
    height: 8,
    backgroundColor: 'white',
    borderRadius: 4,
    marginVertical: 10,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#1BDB55',
    borderRadius: 4,
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 15,
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  scoreIcon: {
    width: 20,
    height: 20,
    marginRight: 5,
  },
  rankIcon: {
    width: 20,
    height: 20,
  },
  scoreText: {
    color: '#333',
    fontWeight: 'bold',
  },
  rankContainer: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  rankText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
    marginLeft: 5,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 10,
  },
  statBox: {
    backgroundColor: '#1BDB55', // Màu xanh lá
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 9,
    borderWidth: 1,
    borderColor: 'white',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  statText: {
    color: '#112164',
    fontWeight: 'bold',
    fontSize: 18,
  },
  questionCard: {
    backgroundColor: '#FCF8E8',
    borderRadius: 15,
    padding: 30,
    marginVertical: 20,
    shadowColor: '#000',
    width: '90%',
    alignSelf: 'center',
  },
  questionText: {
    fontSize: 18,
    color: '#112164', // Màu xanh đậm như trong ảnh
    textAlign: 'center',
    fontWeight: 'bold',
    lineHeight: 26, // Tăng khoảng cách dòng
    marginBottom: 10,
  },
  questionIndexText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  answerButton: {
    backgroundColor: '#FCF8E8',
    borderRadius: 12, // Bo tròn hơn như trong ảnh
    paddingVertical: 10, // Giảm padding top và bottom
    marginVertical: 8, // Giảm khoảng cách giữa các đáp án
    alignItems: 'center',
    justifyContent: 'center', // Căn giữa text
    height: 50, // Chiều cao cố định
    shadowColor: '#000',
    color: '#112164', // Màu xanh đậm như trong ảnh
    width: '80%',
    alignSelf: 'center',
    //thêm thuộc tính shadow bottom
  },
  selectedAnswerButton: {
    backgroundColor: '#1BDB55',
    borderColor: 'white',
    borderWidth: 1,
  },
  correctAnswerButton: {
    backgroundColor: '#1BDB55',
    borderColor: 'white',
    borderWidth: 1,
  },
  wrongAnswerButton: {
    backgroundColor: '#FF5757',
    borderColor: 'white',
    borderWidth: 1,
  },
  answerText: {
    fontSize: 18,
    color: '#003366', // Màu xanh đậm như trong ảnh
    fontWeight: 'bold',
    textAlign: 'center', // Căn giữa text
  },
  selectedAnswerText: {
    color: '#112164',
  },
  placeholderButton: {
    backgroundColor: '#f0f0f0',
    opacity: 0.7,
  },
  placeholderText: {
    color: '#999',
    fontStyle: 'italic',
  },
  bottomToolbar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#FF6B6B',
    paddingVertical: 15,
    borderRadius: 20,
    marginTop: 'auto',
  },
  toolbarButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFA500',
  },
  disabledToolbarButton: {
    backgroundColor: '#D3D3D3',
    opacity: 0.5,
  },
  toolbarButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  moneyLadderContainer: {
    position: 'absolute',
    right: 0,
    top: 0,
    bottom: 0,
    width: width * 0.4,
  },
  closeLadderButton: {
    position: 'absolute',
    top: 10,
    left: 10,
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#EF4444',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeLadderButtonText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#333',
    fontSize: 18,
  },
  errorText: {
    color: '#FF0000',
    fontSize: 18,
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#1BDB55',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: 'white',
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default StartALTP;
