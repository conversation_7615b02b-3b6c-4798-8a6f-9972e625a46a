 
  interface Question {
    id: string;
		level: number;
    title: string;
		listWords: Word[];
		description: string;
		hint: string;
		lastQuestion: boolean;
    sort: number;
  }
   interface Word {
    id: string;
    position: number;
    text: string;
  }
   interface DropZone {
    id: string;
    word: Word | null;
    x: number;
    y: number;
    width: number;
    height: number;
  }

  export type { Word, DropZone, Question};