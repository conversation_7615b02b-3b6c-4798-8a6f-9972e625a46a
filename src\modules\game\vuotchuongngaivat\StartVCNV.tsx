import {
  ActivityIndicator,
  Alert,
  Keyboard,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Vibration,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {MiniQuestion, Word} from './models/models';
import {PanGestureHandler} from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';
import React, {useEffect, useRef, useState} from 'react';
import HeadGame from '../components/HeadGame';
import Lives from '../components/Lives';
import {CardTitleGame} from '../components/CardTitleGame';
import {BottomGame} from '../components/BottomGame';
import {useVcnvHook} from './redux/hooks/vcnvHook';
import {useSelector} from 'react-redux';
import store, {RootState} from '../../../redux/store/store';
import {checkPositionOrder, replaceObjectById} from '../utils/functions';
import {useGameHook} from '../../../redux/hook/gameHook';
import GameOverModal from '../components/GameOverModel';
import HintModel from '../components/HintModel';
import ModelConfirm from '../components/ModelConfirm';
import ModelPauseGame from '../components/ModelPauseGame';
import {useNavigation, useRoute} from '@react-navigation/native';
import ConfigAPI from '../../../Config/ConfigAPI';
import WinnerModal from './components/WinnerModal';
import { GameDA } from '../gameDA';
import { DataController } from '../../../base/baseController';
import { randomGID } from '../../../utils/Utils';
import Sound from 'react-native-sound';

interface DropZone {
  id: string;
  miniQuestionId: string;
  word?: Word | null;
  index: number;
}
// DraggableWord component moved outside to avoid recreation on every render
const DraggableWord = React.memo(({word, refDropZone, onDrop}: {
  word: Word;
  refDropZone: React.MutableRefObject<{[key: string]: View | null}>;
  onDrop: (word: Word, dropZoneId: string) => void;
}) => {
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const scale = useSharedValue(1);
  const zIndex = useSharedValue(0);

  const checkAndHandleDrop = (
    eventAbsoluteX: number,
    eventAbsoluteY: number,
  ) => {
    const dropZoneIds = Object.keys(refDropZone.current);
    let bestMatch: { id: string; distance: number } | null = null;
    let processedCount = 0;

    // Nếu không có drop zone nào, return
    if (dropZoneIds.length === 0) return;

    dropZoneIds.forEach(id => {
      const refCurrent = refDropZone.current[id];
      if (refCurrent) {
        refCurrent.measureInWindow((x, y, width, height) => {
          processedCount++;

          const wordX = eventAbsoluteX;
          const wordY = eventAbsoluteY;
          const dropZoneX = x;
          const dropZoneY = y;
          const dropZoneRight = x + width;
          const dropZoneBottom = y + height;

          // Kiểm tra xem từ có nằm trong drop zone không
          if (
            wordX >= dropZoneX &&
            wordX <= dropZoneRight &&
            wordY >= dropZoneY &&
            wordY <= dropZoneBottom
          ) {
            // Tính khoảng cách từ center của từ đến center của drop zone
            const dropZoneCenterX = x + width / 2;
            const dropZoneCenterY = y + height / 2;
            const distance = Math.sqrt(
              Math.pow(wordX - dropZoneCenterX, 2) +
              Math.pow(wordY - dropZoneCenterY, 2)
            );

            // Chọn drop zone gần nhất
            if (!bestMatch || distance < bestMatch.distance) {
              bestMatch = { id, distance };
            }
          }

          // Khi đã xử lý xong tất cả drop zones, thực hiện drop
          if (processedCount === dropZoneIds.length && bestMatch) {
            onDrop(word, bestMatch.id);
          }
        });
      } else {
        processedCount++;
        // Kiểm tra nếu đã xử lý xong tất cả
        if (processedCount === dropZoneIds.length && bestMatch) {
          onDrop(word, bestMatch.id);
        }
      }
    });
  };

  const gestureHandler = useAnimatedGestureHandler({
    onStart: _ => {
      'worklet';
      scale.value = withSpring(1.1);
      zIndex.value = 1000;
    },
    onActive: event => {
      'worklet';
      translateX.value = event.translationX;
      translateY.value = event.translationY;
    },
    onEnd: event => {
      'worklet';
      runOnJS(checkAndHandleDrop)(event.absoluteX, event.absoluteY);

      translateX.value = withSpring(0);
      translateY.value = withSpring(0);
      scale.value = withSpring(1);
      zIndex.value = 0;
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    'worklet';
    return {
      transform: [
        {translateX: translateX.value},
        {translateY: translateY.value},
        {scale: scale.value},
      ],
      zIndex: zIndex.value,
    };
  });

  return (
    <PanGestureHandler onGestureEvent={gestureHandler}>
      <Animated.View style={[styles.wordContainer, animatedStyle]}>
        <View
          style={{
            height: 36,
            width: 36,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Text style={styles.wordText}>{word.text}</Text>
        </View>
      </Animated.View>
    </PanGestureHandler>
  );
});
const StartVCNV = () => {
  const vcnvHook = useVcnvHook();
  const gameHook = useGameHook();
  const {
    listMiniQuestion,
    currentQuestion,
    gameConfig,
    loading,
    configLoading,
    error,
    configError,
    usedHints,
    crosswordData
  } = useSelector((state: RootState) => state.VTNVStore);
  const {
    currentLives,
    totalLives,
    gem,
    gemUse,
    isGameOver,
    messageGameOver,
  } = useSelector((state: RootState) => state.gameStore);
  const route = useRoute<any>();
  const gameId =  ConfigAPI.gameVTNV;
  const milestoneId = route.params?.milestoneId || 1;
  const competenceId = route.params?.competenceId || '';

  const [listWord, setListWord] = useState<Word[]>([]);
  const [listDropZone, setListDropZone] = useState<DropZone[]>([]);
  const [currentMiniQuestion, setCurrentMiniQuestion] =
    useState<MiniQuestion | null>(null);
  const [answer, setAnswer] = useState<string>('');
  const [isError, setIsError] = useState<boolean>(false);
  const [isCorrect, setIsCorrect] = useState<boolean>(false);
  const [statusMiniQuestion, setStatusMiniQuestion] = useState<
    'correct' | 'wrong' | null
  >(null);
  const [isShowModelConfirm, setIsShowModelConfirm] = useState<boolean>(false);
  const [isShowHintModel, setIsShowHintModel] = useState<boolean>(false);
  const [isPauseGame, setIsPauseGame] = useState<boolean>(false);
  const [isShowKeyboard, setIsShowKeyboard] = useState<boolean>(false);
  const navigation = useNavigation();
  const [listMiniQuestionDone, setListMiniQuestionDone] = useState<
    MiniQuestion[]
  >([]);

  const refDropZone = useRef<{[key: string]: View | null}>({});
  const hiddenInputRef = useRef<TextInput | null>(null);
  const [isWinLevel, setIsWinLevel] = useState<boolean>(false);

  // Audio states
  const [audioPlayer, setAudioPlayer] = useState<Sound | null>(null);
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  // Kiểm tra hint dựa trên câu hỏi từ khóa
  const keywordQuestionText = crosswordData?.keywordQuestion?.hint;
  const hasHint = keywordQuestionText && keywordQuestionText.trim() !== '';
  const keywordQuestionId = crosswordData?.keywordQuestion?.id;
  const isHintUsed = usedHints.includes(keywordQuestionId?.toString() || '');
  const shouldShowHintButton = Boolean(hasHint && !isHintUsed);
  // Đặt resetState lên trước để tránh lỗi dùng trước khi khai báo
  const resetState = React.useCallback(() => {
    setListWord([]);
    setListDropZone([]);
    setCurrentMiniQuestion(null);
    setIsCorrect(false);
    setIsError(false);
    setStatusMiniQuestion(null);
    setIsShowKeyboard(false);
    refDropZone.current = {};
    hiddenInputRef.current?.blur();
  }, []);

  // Sử dụng ref để lưu gameConfig mới nhất, tránh vòng lặp useCallback
  const gameConfigRef = React.useRef(gameConfig);
  useEffect(() => {
    gameConfigRef.current = gameConfig;
  }, [gameConfig]);

  // Đảm bảo initializeGame có dependency đúng, không gây lặp vô hạn
  const initializeGame = React.useCallback(async () => {
    try {
      resetState();
      gameHook.restartGame();
      const result = await vcnvHook.initializeGame({
        gameId,
        milestoneId,
        competenceId,
      });
      const latestGameConfig = gameConfigRef.current;
      if (result.success && latestGameConfig) {
        gameHook.setData({ stateName: 'totalLives', value: latestGameConfig.maxLives });
        gameHook.setData({ stateName: 'currentLives', value: latestGameConfig.maxLives });
        gameHook.setData({ stateName: 'gemAdd', value: latestGameConfig.scorePerLife });
        gameHook.setData({ stateName: 'gemCost', value: latestGameConfig.gemHint });
        gameHook.setData({ stateName: 'time', value: latestGameConfig.timeLimit });
        gameHook.setData({ stateName: 'isRunTime', value: true });
      } else {
        vcnvHook.startGame();
      }
    } catch (err) {
      vcnvHook.startGame();
    }
  }, [gameId, milestoneId, competenceId]);

  useEffect(() => {
    initializeGame();
    fetchScore();
    Keyboard.addListener('keyboardDidShow', () => {
      setIsShowKeyboard(true);
    });
    Keyboard.addListener('keyboardDidHide', () => {
      setIsShowKeyboard(false);
      hiddenInputRef.current?.blur();
    });
    return () => {
      refDropZone.current = {};
      Keyboard.removeAllListeners('keyboardDidShow');
      Keyboard.removeAllListeners('keyboardDidHide');
    };
  }, []); // chỉ chạy 1 lần khi mount
  const fetchScore = async () => {
      try {
        // Lấy thông tin điểm từ bảng GameCUstomer
        const gameDa = new GameDA();
        const result = await gameDa.getScoreByCustomerIdAndGameId(
          store.getState().customer.data.Id,
          ConfigAPI.gameVTNV,
        );
        gameHook.setData({stateName: 'gem', value: result ?? 0});
      } catch (error) {
        console.error('Lỗi khi lấy thông tin điểm:', error);
      }
    };
  useEffect(() => {
    if (currentLives < 1) gameOver('Thất bại rồi, làm lại nào');
  }, [currentLives]);

  useEffect(() => {
    if (listMiniQuestionDone.length === listMiniQuestion?.length) {
      setIsWinLevel(true);
    }
  }, [listMiniQuestionDone, listMiniQuestion?.length]);

  const startGame = () => {
    resetState();
    vcnvHook.startGameWithAPI();

    // Apply gameConfig if available, otherwise use default values
    if (gameConfig) {
      gameHook.setData({stateName: 'totalLives', value: gameConfig.maxLives});
      gameHook.setData({stateName: 'currentLives', value: gameConfig.maxLives});
      gameHook.setData({stateName: 'time', value: gameConfig.timeLimit});
      gameHook.setData({stateName: 'isRunTime', value: true});
      gameHook.setData({stateName: 'isGameOver', value: false});
      gameHook.setData({stateName: 'gemAdd', value: gameConfig.scorePerLife});
      gameHook.setData({stateName: 'gemCost', value: gameConfig.gemHint});
      console.log(`[VCNV] Game restarted with config: ${gameConfig.maxLives} lives, ${gameConfig.timeLimit}s timer`);
    } else {
      gameHook.restartGame();
    }
  };

  const gameOver = React.useCallback((message: string) => {
    gameHook.gameOver(message);
  }, [gameHook]);

  // Tạm dừng game
  const onPauseGame = () => {
    gameHook.pauseGame();
    setIsPauseGame(true);
  };

  // Tiếp tục game
  const onContinueGame = () => {
    gameHook.continueGame();
    setIsPauseGame(false);
  };

  // Hiển thị bàn phím
  const showKeyboard = () => {
    setIsError(false);
    setIsCorrect(false);
    setAnswer('');
    if (hiddenInputRef.current) {
      hiddenInputRef.current.focus();
      refDropZone.current = {};
      setCurrentMiniQuestion(null);
      setTimeout(() => {
        setListWord([]);
      }, 200);
    }
  };
  const useHint = () => {
      if (gem < (gameConfig?.gemHint || 0)) {
        // show model thông báo không đủ gem
        Alert.alert(
          'Thông báo',
          'Bạn không đủ gem để sử dụng gợi ý',
          [
            {
              text: 'OK',
              style: 'cancel',
            },
          ],
          {cancelable: false},
        );
        return;
      }
      gameHook.setData({
        stateName: 'gem',
        value: gem - (gameConfig?.gemHint || 0),
      });
      setIsShowModelConfirm(false);
      updateScore((gameConfig?.gemHint || 0));

      // Mark hint đã được sử dụng cho câu hỏi từ khóa
      if (keywordQuestionId) {
        vcnvHook.markHintUsed(keywordQuestionId);
      }

      console.log('Showing keyword question hint:', keywordQuestionText);
    };
    const updateScore = async (score: number) => {
      debugger
      if(score <= 0) return;
      const gamecustomerController = new DataController('GameCustomer');
      const customerId = store.getState().customer.data.Id;
      const game = {
        Id: randomGID(),
        CustomerId: customerId,
        GameId: ConfigAPI.gameVTNV,
        Stage: milestoneId,
        Competency: competenceId,
        Status: 0,
        DateCreated: new Date().getTime(),
        Score: (-(gameConfig?.gemHint || 0)),
        HighestScore: 0,
        PlayedAt: new Date().getTime(),
        Name: `Sử dụng gợi ý - VCNC_${milestoneId}`,
      };
      console.log('Tạo bản ghi mới:', game);
      const result = await gamecustomerController.add([game]);
      if (result.code !== 200) {
        return false;
      }
      setIsShowHintModel(true);
      return true;
    };

  // Setup audio when current question or mini question changes
  useEffect(() => {
    debugger
    // Ưu tiên audio của mini question nếu có, nếu không thì dùng audio của question chính
    const audioUrl = currentMiniQuestion?.audioUrl || currentQuestion?.audioUrl;

    if (audioUrl) {
      setupAudio(audioUrl);
    } else {
      // Cleanup audio nếu không có audio URL
      if (audioPlayer) {
        audioPlayer.stop();
        audioPlayer.release();
        setAudioPlayer(null);
        setIsPlayingAudio(false);
      }
    }

    // Cleanup previous audio
    return () => {
      if (audioPlayer) {
        audioPlayer.stop();
        audioPlayer.release();
      }
    };
  }, [currentQuestion, currentMiniQuestion]);

  // Setup audio player
  const setupAudio = (audioUrl: string) => {
    console.log('===== SETTING UP AUDIO =====');
    console.log('Audio URL:', audioUrl);

    // Release previous audio
    if (audioPlayer) {
      audioPlayer.stop();
      audioPlayer.release();
    }

    // Enable playback in silence mode (iOS)
    Sound.setCategory('Playback');

    // Create new audio instance
    const sound = new Sound(audioUrl, '', audioError => {
      if (audioError) {
        console.error('Failed to load audio:', audioError);
        return;
      }
      console.log('Audio loaded successfully');
      setAudioPlayer(sound);
    });
  };

  // Play audio function
  const playAudio = () => {
    console.log('===== PLAY AUDIO CALLED =====');
    console.log('Audio player:', audioPlayer);
    console.log('Is playing:', isPlayingAudio);

    if (!audioPlayer) {
      console.log('No audio player available');
      return;
    }

    if (isPlayingAudio) {
      // Stop audio if currently playing
      audioPlayer.stop(() => {
        console.log('Audio stopped');
        setIsPlayingAudio(false);
      });
    } else {
      // Play audio
      setIsPlayingAudio(true);
      audioPlayer.play(success => {
        console.log('Audio play finished, success:', success);
        setIsPlayingAudio(false);
      });
    }
  };

  // Đáp án sai
  const onWrongAnswer = React.useCallback((type: 'normal' | 'key') => {
    // Rung thiết bị khi trả lời sai
    // Vibration.vibrate([0, 500, 200, 500]);

    if (type === 'normal') {
      resetQuestion();
      setStatusMiniQuestion('wrong');
      setTimeout(() => {
        setStatusMiniQuestion(null);
      }, 1000);
    } else {
      setIsError(true);
    }
    gameHook.setData({stateName: 'currentLives', value: currentLives - 1});
  }, [currentLives]);

  // Đáp án đúng
  const onCorrectAnswer = React.useCallback((type: 'normal' | 'key') => {
    if (type === 'normal') {
      setStatusMiniQuestion('correct');
      setListMiniQuestionDone(prev => [
        ...prev,
        currentMiniQuestion as MiniQuestion,
      ]);
      setTimeout(() => {
        setStatusMiniQuestion(null);
      }, 2000);
    } else {
      setIsCorrect(true);
      setTimeout(() => {
        setIsCorrect(false);
      }, 2000);
    }
  }, [currentMiniQuestion]);

  // Reset câu hỏi
  const resetQuestion = React.useCallback(() => {
    if (!currentMiniQuestion?.id) return;

    const newListWord: Word[] = listDropZone
      .filter(
        zone => zone.miniQuestionId === currentMiniQuestion.id && zone.word,
      )
      .map(zone => zone.word as Word);

    setListWord(newListWord);

    const updatedListDropZone = listDropZone.map(zone =>
      zone.miniQuestionId === currentMiniQuestion.id && zone.word
        ? {...zone, word: null}
        : zone,
    );
    setListDropZone(updatedListDropZone);

    const updatedListMiniQuestion = replaceObjectById(
      {...currentMiniQuestion, listWord: newListWord},
      listMiniQuestion,
    );

    vcnvHook.setData({
      stateName: 'listMiniQuestion',
      value: updatedListMiniQuestion,
    });
  }, [currentMiniQuestion, listDropZone, listMiniQuestion, vcnvHook]);

  // Kiểm tra kết quả của câu hàng ngang
  const checkAnswerRow = React.useCallback(() => {
    const zoneOfCurrentMiniQuestion = listDropZone.filter(
      zone => zone.miniQuestionId === currentMiniQuestion?.id && zone.word,
    );
    zoneOfCurrentMiniQuestion.sort((a, b) => a.index - b.index);

    const wordsInOrder = zoneOfCurrentMiniQuestion.map(zone => zone.word);

    const isAnswerCorrect = checkPositionOrder(wordsInOrder);
    if (isAnswerCorrect) {
      onCorrectAnswer('normal');
    } else {
      onWrongAnswer('normal');
    }
  }, [listDropZone, currentMiniQuestion, onCorrectAnswer, onWrongAnswer]);

  // Khi chọn hàng ngang
  const chooseRow = (miniQuestion: MiniQuestion) => {
    refDropZone.current = {};
    setCurrentMiniQuestion(miniQuestion);
    setTimeout(() => {
      setListWord(miniQuestion.listWord);
    }, 200);
  };

  // Thả word vào dropzone
  const addWordToDropZone = React.useCallback((word: Word, dropZoneId: string) => {
    // Thêm word vào dropZone
    const dropZone = listDropZone.find(zone => zone.id === dropZoneId);
    if (dropZone) {
      if (dropZone.word) return;
      dropZone.word = word;
      const newListDropZone = replaceObjectById(dropZone, listDropZone);
      setListDropZone(newListDropZone);

      // Xoá word khỏi listWord
      const newListWord = listWord.filter(w => w.id !== word.id);
      setListWord(newListWord);

      // Sửa lại listMiniQuestion
      const cloneCurrentMiniQuestion = {...currentMiniQuestion};
      if (cloneCurrentMiniQuestion) {
        cloneCurrentMiniQuestion.listWord = [...newListWord];
        const newListMiniQuestion = replaceObjectById(
          cloneCurrentMiniQuestion,
          listMiniQuestion,
        );
        vcnvHook.setData({
          stateName: 'listMiniQuestion',
          value: newListMiniQuestion,
        });
      }

      // nếu listWord rỗng thì check answer
      if (newListWord.length === 0) {
        checkAnswerRow();
      }
    }
  }, [listDropZone, listWord, currentMiniQuestion, listMiniQuestion, vcnvHook, checkAnswerRow]);

  // CLick vào dropzone có chứa text
  const clickDropZoneWord = (dropZone: DropZone) => {
    const newListWord = [...listWord];
    if (newListWord) {
      newListWord.push(dropZone.word as Word);
    }
    // Thêm word vào listWord
    setListWord(newListWord);

    // // Sửa lại listMiniQuestion
    const cloneCurrentMiniQuestion = {...currentMiniQuestion};
    cloneCurrentMiniQuestion.listWord = newListWord;
    const newListMiniQuestion = replaceObjectById(
      cloneCurrentMiniQuestion,
      listMiniQuestion,
    );
    vcnvHook.setData({
      stateName: 'listMiniQuestion',
      value: newListMiniQuestion,
    });

    dropZone.word = null;
    const newListDropZone = replaceObjectById(dropZone, listDropZone);
    setListDropZone(newListDropZone);
  };

  // Kiểm tra đáp án hàng dọc key
  const checkAnswer = () => {
    setIsError(false);
    setIsCorrect(false);
    if (answer.toLowerCase() === currentQuestion?.answer) {
      setIsCorrect(true);
      setIsWinLevel(true);
    } else {
      onWrongAnswer('key');
    }
  };



  // Tính toán cột key chung tối ưu cho tất cả các hàng
  const optimalKeyColumn = React.useMemo(() => {
    if (!listMiniQuestion || listMiniQuestion.length === 0) {
      return 5; // Mặc định cột giữa
    }

    let bestKeyColumn = 5;
    let maxAlignableCount = 0;

    // Thử từng vị trí cột key từ 1 đến 9
    for (let testKeyCol = 1; testKeyCol <= 9; testKeyCol++) {
      let alignableCount = 0;

      // Kiểm tra từng câu hỏi có thể thẳng hàng với cột key này không
      for (const question of listMiniQuestion) {
        const totalWords = question.length;
        const keyWordPosition = question.keyIndex;
        const startColumn = testKeyCol - keyWordPosition + 1;
        const endColumn = startColumn + totalWords - 1;

        // Kiểm tra có fit trong 9 cột không
        if (startColumn >= 1 && endColumn <= 9) {
          alignableCount++;
        }
      }

      // Chọn cột key cho phép nhiều hàng thẳng hàng nhất
      if (alignableCount > maxAlignableCount) {
        maxAlignableCount = alignableCount;
        bestKeyColumn = testKeyCol;
      }
    }

    console.log(`[VCNV] Optimal key column: ${bestKeyColumn} (${maxAlignableCount}/${listMiniQuestion.length} questions can align)`);
    return bestKeyColumn;
  }, [listMiniQuestion]);

  // Render drop zone
  const renderDropZone = (index: number, miniQuestion: MiniQuestion) => {
    index = index + 1;

    const totalWords = miniQuestion.length;
    const keyWordPosition = miniQuestion.keyIndex; // Vị trí của từ key trong câu (1-based)
    const globalKeyColumn = optimalKeyColumn;

    let startColumn: number;
    let endColumn: number;
    let actualKeyColumn: number;
    let canAlign = false;

    // Thử thẳng hàng với cột key chung trước
    const alignedStartColumn = globalKeyColumn - keyWordPosition + 1;
    const alignedEndColumn = alignedStartColumn + totalWords - 1;

    // Kiểm tra xem có thể thẳng hàng không
    if (alignedStartColumn >= 1 && alignedEndColumn <= 9) {
      // Trường hợp 1: Có thể thẳng hàng với cột key chung
      startColumn = alignedStartColumn;
      endColumn = alignedEndColumn;
      actualKeyColumn = globalKeyColumn;
      canAlign = true;
    } else {
      // Trường hợp 2: Không thể thẳng hàng - căn giữa và tính key column riêng
      const totalColumns = 9;
      const leftPadding = Math.floor((totalColumns - totalWords) / 2);
      startColumn = leftPadding + 1;
      endColumn = startColumn + totalWords - 1;
      actualKeyColumn = startColumn + keyWordPosition - 1;
      canAlign = false;
    }

    // Debug log
    if (index === 1) {
      console.log(`[VCNV Debug] Question ${miniQuestion.id}: totalWords=${totalWords}, keyIndex=${keyWordPosition}, startColumn=${startColumn}, endColumn=${endColumn}, actualKeyColumn=${actualKeyColumn}, canAlign=${canAlign}, globalKeyColumn=${globalKeyColumn}`);
    }

    // Kiểm tra ô này có được hiển thị không
    const isShow = index >= startColumn && index <= endColumn;

    // Kiểm tra ô này có phải là cột Key không
    const isKeyColumn = index === actualKeyColumn;

    const isChoose =
      currentMiniQuestion?.id === miniQuestion.id ||
      (isKeyColumn && isShowKeyboard);
    const dropZone = listDropZone.find(
      zone => zone.id === `${miniQuestion.id}${index}`,
    );
    if (!dropZone) {
      listDropZone.push({
        id: `${miniQuestion.id}${index}`,
        miniQuestionId: miniQuestion.id,
        word: null,
        index,
      });
    }
    return isShow ? (
      <TouchableOpacity
        onPress={() => {
          if (listMiniQuestionDone.find(i => i.id === miniQuestion.id)) return;
          if (isChoose && dropZone?.word) return clickDropZoneWord(dropZone);
          chooseRow(miniQuestion);
        }}
        ref={ref => {
          if (
            miniQuestion.id === currentMiniQuestion?.id &&
            refDropZone.current
          ) {
            refDropZone.current[`${miniQuestion.id}${index}`] = ref;
          }
        }}
        key={index}
        style={[
          {
            width: 36,
            height: 36,
            margin: 1,
            borderRadius: 4,
            backgroundColor: isKeyColumn ? '#1BDB55' : 'white',
          },

          isChoose
            ? {
                borderWidth: 1,
                borderColor: '#AE2B26',
              }
            : {
                backgroundColor: isKeyColumn ? '#1BDB55' : '#E8E8E8',
              },
        ]}>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Text style={styles.wordText}>{dropZone?.word?.text || ''}</Text>
        </View>
      </TouchableOpacity>
    ) : (
      <View key={index} style={{width: 36, height: 36, margin: 1}}></View>
    );
  };

  // Show loading UI while data is being loaded
  if (loading || configLoading || !listMiniQuestion || !gameConfig) {
    return (
      <SafeAreaView style={{flex: 1, backgroundColor: '#FFC670'}}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#1BDB55" />
          <Text style={styles.loadingText}>Đang tải dữ liệu game...</Text>
          {configLoading && (
            <Text style={styles.loadingSubText}>Đang tải cấu hình game</Text>
          )}
          {loading && (
            <Text style={styles.loadingSubText}>Đang tải câu hỏi</Text>
          )}
        </View>
      </SafeAreaView>
    );
  }

  // Show error UI if failed to load data
  if (error || configError) {
    return (
      <SafeAreaView style={{flex: 1, backgroundColor: '#FFC670'}}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Lỗi tải dữ liệu</Text>
          <Text style={styles.errorMessage}>
            {error || configError || 'Không thể tải dữ liệu game'}
          </Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={initializeGame}
          >
            <Text style={styles.retryButtonText}>Thử lại</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: '#FFC670'}}>
      <View style={{flex: 1}}>
        {/* Header */}
        <View style={{marginHorizontal: 16}}>
          <HeadGame
            timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
            isShowSuggest={shouldShowHintButton}
            onUseHint={() => setIsShowModelConfirm(true)}
            gameId={ConfigAPI.gameVTNV}
          />
          <View style={{marginTop: 8}}>
            <Lives totalLives={totalLives} currentLives={currentLives} />
          </View>
        </View>

        {/* Body */}
        {
          isPauseGame ? (
            <View
              style={{
                zIndex: 1000,
              }}>
              <ModelPauseGame
          visible={isPauseGame}
          message={'Bạn đang tạm dừng trò chơi'}
          onContinue={onContinueGame}
        />
            </View>
          ) : <View
          style={{
            flex: 1,
            marginHorizontal: 12,
            borderRadius: 16,
          }}>
          <View style={{marginVertical: 6}}>
            {statusMiniQuestion === 'correct' && (
              <Text style={styles.correctText}>Đáp án chính xác</Text>
            )}
            {statusMiniQuestion === 'wrong' && (
              <Text style={styles.errorText}>Sai rồi, hãy thử đáp án khác</Text>
            )}
          </View>
          {listMiniQuestion?.map(
            (miniQuestion: MiniQuestion, rowIndex: number) => {
              return (
                <View style={{flexDirection: 'row'}} key={rowIndex}>
                  {Array.from({length: 9}).map((_, colIndex) => {
                    return renderDropZone(colIndex, miniQuestion);
                  })}
                </View>
              );
            },
          )}
          <View style={{flex: 1, justifyContent: 'flex-end'}}>
            {!isShowKeyboard ? (
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  marginTop: 16,
                }}>
                <TouchableOpacity
                  style={[styles.checkButton, {backgroundColor: '#1BDB55'}]}
                  onPress={showKeyboard}>
                  <Text style={styles.checkButtonText}>Giải từ khoá</Text>
                </TouchableOpacity>
              </View>
            ) : null}
            <View>
              <View style={styles.questionContainer}>
                {/* Audio button - hiển thị khi có audio (ưu tiên mini question, fallback về question chính) */}
                {(currentMiniQuestion?.audioUrl || (!currentMiniQuestion && currentQuestion?.audioUrl)) && (
                  <TouchableOpacity
                    style={styles.audioButton}
                    onPress={playAudio}>
                    <Text style={styles.audioIcon}>
                      {isPlayingAudio ? '⏸️' : '🔊'}
                    </Text>
                  </TouchableOpacity>
                )}

                {/* Question title */}
                <View style={styles.questionTitleContainer}>
                  <Text style={styles.questionTitle}>
                    {!currentMiniQuestion
                      ? currentQuestion?.text || ''
                      : currentMiniQuestion?.text || ''}
                  </Text>
                </View>
              </View>
            </View>
            <View style={styles.wordsContainer}>
              {listWord.map((word: Word, index) => (
                <DraggableWord
                  key={index}
                  word={word}
                  refDropZone={refDropZone}
                  onDrop={addWordToDropZone}
                />
              ))}
            </View>
          </View>

          {isShowKeyboard ? (
            <View
              style={{
                width: '100%',
                height: 200,
                marginTop: 50,
                position: 'absolute',
              }}>
              <View
                style={[
                  styles.answerContainer,
                  isError ? styles.answerContainerError : null,
                  isCorrect ? styles.answerContainerCorrect : null,
                ]}>
                {answer.length < 1 ? (
                  <Text
                    style={{
                      color: '#999',
                      textAlign: 'center',
                      fontSize: 14,
                      fontStyle: 'italic',
                    }}>
                    Vui lòng nhập đáp án
                  </Text>
                ) : (
                  <Text style={styles.answerText}>{answer}</Text>
                )}
                {isCorrect && (
                  <Text style={styles.correctText}>Đáp án chính xác</Text>
                )}
                {isError && (
                  <Text style={styles.errorText}>
                    Sai rồi, hãy thử đáp án khác
                  </Text>
                )}
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  marginTop: 16,
                }}>
                <View style={{width: 16}}></View>
                <TouchableOpacity
                  style={[styles.checkButton, {backgroundColor: '#D32F2F'}]}
                  onPress={checkAnswer}>
                  <Text style={styles.checkButtonText}>Kiểm tra đáp án</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : null}
        </View>
        }
        
        {/* Bottom */}
        <View style={styles.bottomContainer}>
          <BottomGame
            resetGame={startGame}
            backGame={() => { navigation.goBack()}}
            pauseGame={onPauseGame}
            volumeGame={() => {}}
          />
        </View>
        <TextInput
          ref={hiddenInputRef}
          style={styles.hiddenInput}
          value={answer}
          onChangeText={text => setAnswer(text)}
          autoCapitalize="none"
          autoCorrect={false}
          spellCheck={false}
          caretHidden={true}
        />
      </View>
      <View style={styles.modalContainer}>
        <ModelConfirm
          isShow={isShowModelConfirm}
          closeModal={() => setIsShowModelConfirm(false)}
          onConfirm={useHint}
        />
        <HintModel
          isShow={isShowHintModel}
          closeModal={() => setIsShowHintModel(false)}
          text={crosswordData?.keywordQuestion?.hint || currentQuestion?.hint || ''}
        />
        <GameOverModal
          visible={isGameOver}
          onClose={() => {}}
          restartGame={startGame}
          message={messageGameOver}
          isTimeOut={false}
        />        
        <WinnerModal
          visible={isWinLevel}
          onClose={() => {
            setIsWinLevel(false);
          }}
          restartGame={startGame}
          currentLives={currentLives}
          competenceId={competenceId}
          gameId={ConfigAPI.gameVTNV}
        />
      </View>
    </SafeAreaView>
  );
};
 export default StartVCNV;

const styles = StyleSheet.create({
  wordContainer: {
    margin: 6,
    backgroundColor: '#FCF8E8',
    borderRadius: 10,
    shadowColor: '#32325D',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  wordText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#112164',
  },
  wordsContainer: {
    marginTop: 16,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: 70,
  },
  checkButton: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignSelf: 'center',
    marginBottom: 30,
  },
  checkButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    marginHorizontal: 16,
  },
  answerContainer: {
    position: 'relative',
    alignItems: 'flex-start',
    marginTop: 32,
    backgroundColor: '#FCF8E8',
    borderRadius: 15,
    paddingVertical: 32,
    paddingHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  answerContainerError: {
    borderColor: '#FF6B6B',
    borderWidth: 3,
    borderRadius: 12,
  },
  answerContainerCorrect: {
    borderColor: '#2EB553',
    borderWidth: 3,
    borderRadius: 12,
  },
  answerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  errorText: {
    fontSize: 12,
    textAlign: 'center',
    backgroundColor: '#FDE83280',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    color: '#FF6B6B',
    fontWeight: 'bold',
    marginTop: 5,
  },
  correctText: {
    fontSize: 12,
    textAlign: 'center',
    backgroundColor: '#FDE83280',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    color: '#2EB553',
    fontWeight: 'bold',
    marginTop: 5,
  },
  modalContainer: {
    zIndex: 1000,
  },
  hiddenInput: {
    width: 0, // Chiều rộng 0
    height: 0, // Chiều cao 0
    opacity: 0, // Hoàn toàn trong suốt
    position: 'absolute', // Không chiếm không gian trong layout
    // Để đảm bảo nó thực sự không nhìn thấy và không tương tác ngoài ý muốn:
    top: -10000, // Đẩy ra rất xa màn hình
    left: -10000,
  },
  // Loading UI styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#112164',
    marginTop: 16,
    textAlign: 'center',
  },
  loadingSubText: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
  },
  // Error UI styles
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#D32F2F',
    marginBottom: 16,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  retryButton: {
    backgroundColor: '#1BDB55',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  // Audio styles
  questionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FCF8E8',
    padding: 12,
    borderRadius: 12,
  },
  audioButton: {
    marginRight: 10,
    padding: 6,
    borderRadius: 18,
    width: 36,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
  },
  audioIcon: {
    fontSize: 18,
  },
  questionTitleContainer: {
    flex: 1,
  },
  questionTitle: {
    fontSize: 16,
    color: '#333',
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

