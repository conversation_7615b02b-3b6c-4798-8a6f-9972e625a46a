// Legacy interfaces - kept for backward compatibility
interface Word {
  id: string;
  text: string;
  isKey: boolean;
  position: number;
}

interface MiniQuestion {
  id: string;
  text: string;
  length: number;
  keyIndex: number;
  indexStart: number;
  listWord: Word[];
}

interface Question {
  id: string;
  text: string;
  hint: string;
  answer: string;
  miniQuestions: MiniQuestion[];
  audioUrl?: string; // URL file audio (optional)
}

// New interfaces for API integration
interface VCNVWord {
  id: string;
  text: string;
  correctPosition: number;
  isKey: boolean;
}

interface VCNVQuestion {
  id: string;
  questionText: string;
  sort: number;
  words: VCNVWord[];
  keywordAnswer?: string;
  stage: number;
  competenceId: string;
  hint: string;
  audioUrl?: string; // URL file audio (optional)
}

interface VCNVCrosswordData {
  horizontalQuestions: VCNVQuestion[];
  keywordQuestion: VCNVQuestion;
  gridLayout: VCNVGridCell[][];
}

interface VCNVGridCell {
  row: number;
  col: number;
  questionId?: string;
  wordIndex?: number;
  isKeyColumn: boolean;
  isActive: boolean;
}

// Utility functions for converting between old and new formats
const convertVCNVWordToWord = (vcnvWord: VCNVWord): Word => ({
  id: vcnvWord.id,
  text: vcnvWord.text,
  isKey: vcnvWord.isKey,
  position: vcnvWord.correctPosition,
});

const convertVCNVQuestionToMiniQuestion = (vcnvQuestion: VCNVQuestion): MiniQuestion => {
  const keyIndex = vcnvQuestion.words.findIndex(w => w.isKey) + 1; // Convert to 1-based
  console.log(`[VCNV Convert] Question ${vcnvQuestion.id}: words=${vcnvQuestion.words.length}, keyIndex=${keyIndex}, sort=${vcnvQuestion.sort}`);
  return {
    id: vcnvQuestion.id,
    text: vcnvQuestion.questionText,
    length: vcnvQuestion.words.length,
    keyIndex: keyIndex,
    indexStart: 1, // Will be calculated dynamically
    listWord: vcnvQuestion.words.map(convertVCNVWordToWord),
  };
};

const convertCrosswordDataToLegacyFormat = (crosswordData: VCNVCrosswordData): Question => ({
  id: crosswordData.keywordQuestion.id,
  text: crosswordData.keywordQuestion.questionText,
  hint: crosswordData.keywordQuestion.hint,
  answer: crosswordData.keywordQuestion.keywordAnswer || '',
  audioUrl: crosswordData.keywordQuestion.audioUrl, // Thêm audio URL
  miniQuestions: crosswordData.horizontalQuestions.map(convertVCNVQuestionToMiniQuestion),
});

export type {
  Word,
  MiniQuestion,
  Question,
  VCNVWord,
  VCNVQuestion,
  VCNVCrosswordData,
  VCNVGridCell
};

export {
  convertVCNVWordToWord,
  convertVCNVQuestionToMiniQuestion,
  convertCrosswordDataToLegacyFormat
};
