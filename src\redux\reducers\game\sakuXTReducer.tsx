import {createSlice, createAsyncThunk} from '@reduxjs/toolkit';
import {SakuXTDA} from '../../../modules/game/sakuxayto/da/sakuXTDA';
import {SakuXTGameState} from '../../../modules/game/sakuxayto/types/sakuXTTypes';

// Async thunk để load game config từ API
export const loadSakuXTGameConfig = createAsyncThunk(
  'sakuXT/loadGameConfig',
  async ({gameId}: {gameId: string}) => {
    try {
      console.log(`[Redux] Loading SakuXT game config for GameId: ${gameId}`);
      const config = await SakuXTDA.getGameConfig(gameId);
      return config;
    } catch (error) {
      console.error('[Redux] Failed to load SakuXT game config:', error);
      throw error;
    }
  },
);

// Async thunk để load questions và answers từ API
export const loadSakuXTQuestions = createAsyncThunk(
  'sakuXT/loadQuestions',
  async ({
    gameId,
    stage,
    competenceId,
  }: {
    gameId: string;
    stage: number;
    competenceId: string;
  }) => {
    try {
      console.log(
        `[Redux] Loading SakuXT questions for GameId: ${gameId}, Stage: ${stage}, CompetenceId: ${competenceId}`,
      );

      const sakuXTDA = new SakuXTDA();
      const rawQuestions = await sakuXTDA.getQuestionsByGameAndStage(
        gameId,
        stage,
        competenceId,
      );

      if (rawQuestions.length === 0) {
        throw new Error('No questions found for this game');
      }

      // Transform data - answers đã được load trong getQuestionsByGameAndStage
      const transformedQuestions =
        SakuXTDA.transformQuestionsWithAnswers(rawQuestions);
      return transformedQuestions;
    } catch (error) {
      console.error('[Redux] Failed to load SakuXT questions:', error);
      throw error;
    }
  },
);

const initialState: SakuXTGameState = {
  // API Data
  questions: [],
  gameConfig: null,

  // Current Question
  currentQuestionIndex: 0,
  currentQuestion: null,
  availableWords: [],

  // Game State
  wordsInDropZone: [],
  wordsInCheck: [],
  wordsOutSideZone: [],
  isAnswerCorrect: null,
  feedbackMessage: '',
  feedbackType: null,

  // Progress
  questionDone: 0,
  totalQuestion: 0,
  currentStage: 1,

  // Scoring & Config (from GameConfig API)
  maxLives: 3,
  currentLives: 3,
  timeLimit: 300,
  timeRemaining: 300,
  scorePerLife: 10,
  bonusScore: 50,
  currentScore: 0,

  // API State
  loading: false,
  configLoading: false,
  error: null,
  configError: null,
  initialized: false,
  configInitialized: false,
};

export const SakuXTReducer = createSlice({
  name: 'sakuXTReducer',
  initialState,
  reducers: {
    setData(state, action) {
      const {stateName, value} = action.payload;
      (state as any)[stateName] = value;
    },

    // Khởi tạo game với question đầu tiên
    initializeGame(state) {
      if (state.questions.length > 0) {
        state.wordsInDropZone = [];
        state.wordsOutSideZone = [];
        const shuffledWords = [
          ...state.questions[state.questionDone].words,
        ].sort(() => Math.random() - 0.5);
        const halfLength = shuffledWords.length / 2;

        const firstHalf = shuffledWords.slice(0, halfLength);
        const secondHalf = shuffledWords.slice(halfLength);
        state.currentQuestionIndex = 0;
        state.currentQuestion = state.questions[0];
        state.availableWords = [...state.currentQuestion.words];
        state.wordsInCheck = [];

        state.wordsInDropZone = [...firstHalf];
        state.wordsOutSideZone = [...secondHalf];
        state.questionDone = 0;
        state.isAnswerCorrect = null;
        state.feedbackMessage = '';
        state.feedbackType = null;
        state.initialized = true;
      }
    },

    // Chuyển sang question tiếp theo
    nextQuestion(state) {
      if (state.currentQuestionIndex < state.questions.length - 1) {
        state.currentQuestionIndex += 1;
        state.currentQuestion = state.questions[state.currentQuestionIndex];
        state.availableWords = [...state.currentQuestion.words];
        state.questionDone += 1;
        const shuffledWords = [
          ...state.questions[state.questionDone].words,
        ].sort(() => Math.random() - 0.5);
        const halfLength = shuffledWords.length / 2;

        const firstHalf = shuffledWords.slice(0, halfLength);
        const secondHalf = shuffledWords.slice(halfLength);
        state.wordsInCheck = [];
        state.wordsInDropZone = [...firstHalf];
        state.wordsOutSideZone = [...secondHalf];
        state.isAnswerCorrect = null;
        state.feedbackMessage = '';
        state.feedbackType = null;
      }
    },

    // Kiểm tra đáp án
    checkAnswer(state) {
      if (state.currentQuestion && state.wordsInCheck.length > 0) {
        // Sắp xếp words trong drop zone theo currentPosition
        // Sắp xếp words trong drop zone theo currentPosition
        const sortedUserAnswer = [...state.wordsInCheck];

        // Sắp xếp correct answer theo correctPosition
        const sortedCorrectAnswer = [
          ...state.currentQuestion.words.filter(word => word.IsResult),
        ].sort((a, b) => a.correctPosition - b.correctPosition);

        // case: wordsInDropZone is empty
        if (!sortedUserAnswer || sortedUserAnswer.length === 0) {
          console.log('No words in drop zone - reducing lives');
          // Không có từ nào -> sai -> trừ mạng
          state.isAnswerCorrect = false;
          state.feedbackMessage = 'Đáp án sai rồi. hãy thử lại';
          state.feedbackType = 'error';
          state.currentLives -= 1;

          return;
        }

        // Case 1: Nếu sortedUserAnswer có 1 phần tử IsResult == false => return isCorrect = false
        if (sortedUserAnswer.some(word => !word.IsResult)) {
          state.isAnswerCorrect = false;
          state.feedbackMessage = 'Đáp án sai rồi. hãy thử lại';
          state.feedbackType = 'error';
          state.currentLives -= 1;
          return;
        }

        // Case 2: Nếu sortedUserAnswer.length !== sortedCorrectAnswer.length return isCorrect = false
        if (sortedUserAnswer.length !== sortedCorrectAnswer.length) {
          state.isAnswerCorrect = false;
          state.feedbackMessage = 'Đáp án sai rồi. hãy thử lại';
          state.feedbackType = 'error';
          state.currentLives -= 1;
          return;
        }

        // Case 3: Kiểm tra objects ở 2 list sortedUserAnswer và sortedCorrectAnswer có giống nhau không, có cùng thứ tự không
        const isCorrect = sortedUserAnswer.every((word, index) => {
          const correctWord = sortedCorrectAnswer[index];
          return (
            word.id === correctWord.id ||
            word.text.normalize().trim().toLowerCase() ===
              correctWord.text.normalize().trim().toLowerCase()
          );
        });

        console.log('isCorrect', isCorrect);

        if (isCorrect) {
          state.feedbackMessage = 'Đáp án chính xác.';
          state.feedbackType = 'success';
          state.wordsInCheck = [];
          state.wordsInDropZone = [];
          state.wordsOutSideZone = [];
        } else {
          state.feedbackMessage = 'Đáp án sai rồi. hãy thử lại';
          state.feedbackType = 'error';
          state.currentLives -= 1;
        }
        state.isAnswerCorrect = isCorrect;
      } else {
        state.isAnswerCorrect = false;
        state.feedbackMessage = 'Đáp án sai rồi. hãy thử lại';
        state.feedbackType = 'error';
        state.currentLives -= 1;
      }
    },

    // Reset feedback
    clearFeedback(state) {
      state.feedbackMessage = '';
      state.feedbackType = null;
      state.isAnswerCorrect = null;
    },

    // Update timer
    updateTimer(state) {
      if (state.timeRemaining > 0) {
        state.timeRemaining -= 1;
      }
    },

    // Start timer
    startTimer(state) {
      state.timeRemaining = state.timeLimit;
    },

    // Stop timer
    stopTimer(state) {
      state.timeRemaining = 0;
    },

    // Reset game
    reset(state) {
      if (state.questions.length > 0) {
        // Reset to initial state but keep API data and config
        const shuffledWords = [
          ...state.questions[state.questionDone].words,
        ].sort(() => Math.random() - 0.5);
        const halfLength = shuffledWords.length / 2;

        const firstHalf = shuffledWords.slice(0, halfLength);
        const secondHalf = shuffledWords.slice(halfLength);
        state.currentQuestionIndex = 0;
        state.currentQuestion =
          state.questions.length > 0
            ? state.questions[state.questionDone]
            : null;
        state.availableWords = state.currentQuestion
          ? [...state.currentQuestion.words]
          : [];
        state.wordsInCheck = [];
        state.wordsInDropZone = firstHalf;
        state.wordsOutSideZone = secondHalf;
        state.questionDone = 0;
        state.isAnswerCorrect = null;
        state.feedbackMessage = '';
        state.feedbackType = null;
        state.timeRemaining = state.timeLimit;
      }
    },
  },

  extraReducers: builder => {
    // Load Game Config
    builder
      .addCase(loadSakuXTGameConfig.pending, state => {
        state.configLoading = true;
        state.configError = null;
      })
      .addCase(loadSakuXTGameConfig.fulfilled, (state, action) => {
        state.configLoading = false;
        state.gameConfig = action.payload;

        // Apply config to game state
        const config = action.payload;
        state.maxLives = config.maxLives;
        state.currentLives = config.maxLives;
        state.timeLimit = config.timeLimit;
        state.timeRemaining = config.timeLimit;
        state.scorePerLife = config.scorePerLife;
        state.bonusScore = config.bonusScore;
        state.configInitialized = true;
      })
      .addCase(loadSakuXTGameConfig.rejected, (state, action) => {
        state.configLoading = false;
        state.configError =
          action.error.message || 'Failed to load game config';
        console.error(
          '[Redux] Failed to load SakuXT game config:',
          action.error,
        );
      });

    // Load Questions
    builder
      .addCase(loadSakuXTQuestions.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loadSakuXTQuestions.fulfilled, (state, action) => {
        state.loading = false;
        state.questions = action.payload;
        state.totalQuestion = action.payload.length;

        // Initialize first question
        if (action.payload.length > 0) {
          state.currentQuestion = action.payload[0];
          state.availableWords = [...action.payload[0].words];
        }

        state.initialized = true;
      })
      .addCase(loadSakuXTQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to load questions';
        console.error('[Redux] Failed to load SakuXT questions:', action.error);
      });
  },
});

export const {
  setData,
  initializeGame,
  nextQuestion,
  checkAnswer,
  clearFeedback,
  updateTimer,
  startTimer,
  stopTimer,
  reset,
} = SakuXTReducer.actions;

export default SakuXTReducer.reducer;
