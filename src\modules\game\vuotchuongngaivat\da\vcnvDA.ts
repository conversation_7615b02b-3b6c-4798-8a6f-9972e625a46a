import { DataController } from '../../../../base/baseController';
import { BaseDA } from '../../../../base/BaseDA';
import {
  VCNVGameConfigAPI,
  VCNVGameQuestionAPI,
  VCNVGameAnswerAPI,
  VCNVGameConfig,
  VCNVQuestion,
  VCNVWord,
  VCNVCrosswordData,
  VCNVGridCell,
  ApiResponse,
  JapaneseSplitResponse,
  GetVCNVQuestionsResponse,
  GetVCNVAnswersResponse,
  GetVCNVConfigResponse,
} from '../types/vcnvTypes';

export class VCNVDA {
  private questionController: DataController;
  private answerController: DataController;

  constructor() {
    this.questionController = new DataController('GameQuestion');
    this.answerController = new DataController('GameAnswer');
  }

  /**
   * L<PERSON>y c<PERSON>u hình game từ bảng GameConfig
   * @param gameId ID của game VCNV
   * @returns Promise<VCNVGameConfig>
   */
  static async getGameConfig(gameId: string): Promise<VCNVGameConfig> {
    try {
      console.log(`[VCNVDA] Loading game config for GameId: ${gameId}`);

      const controller = new DataController('GameConfig');
      const response: GetVCNVConfigResponse = await controller.getListSimple({
        query: `@GameId: {${gameId}}`,
      });

      if (response.code !== 200 || !response.data || response.data.length === 0) {
        throw new Error('No game config found or API returned unsuccessful response');
      }
      const configData = response.data[0];
      return {
        gameId: configData.GameId,
        scorePerLife: configData.Score,
        maxLives: configData.LifeCount,
        timeLimit: configData.Time,
        bonusScore: configData.Bonus,
        isActive: configData.IsActive,
        gemHint: configData.ScoreHint,
      };
    } catch (error) {
      console.error('[VCNVDA] Error loading game config:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách câu hỏi từ bảng GameQuestion
   * @param gameId ID của game
   * @param stage Stage của game
   * @param competenceId ID competence
   * @returns Promise<VCNVGameQuestionAPI[]>
   */
  async getQuestionsByGameAndStage(
    gameId: string,
    stage: number,
    competenceId: string
  ): Promise<VCNVGameQuestionAPI[]> {
    try {
      console.log(`[VCNVDA] Loading questions for GameId: ${gameId}, Stage: ${stage}, CompetenceId: ${competenceId}`);

      const response: GetVCNVQuestionsResponse = await this.questionController.getListSimple({
        query: `@GameId: {${gameId}} @Stage: [${stage}] @Purpose: [${competenceId}]`,
        sortby: { BY: 'Sort', DIRECTION: 'ASC' },
      });

      if (response.code !== 200) {
        throw new Error(`API returned error code: ${response.code}`);
      }

      if (!response.data || response.data.length === 0) {
        throw new Error('No questions found for the specified criteria');
      }

      // Validate có đủ 9 câu hỏi (8 hàng ngang + 1 từ khóa dọc)
      if (response.data.length !== 9) {
        throw new Error(`Expected 9 questions, but got ${response.data.length}`);
      }

      // Validate có câu hỏi sort = 9 (từ khóa dọc)
      const keywordQuestion = response.data.find(q => q.Sort === 9);
      if (!keywordQuestion) {
        throw new Error('Keyword question (Sort = 9) not found');
      }

      return response.data;
    } catch (error) {
      console.error('[VCNVDA] Error loading questions:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách đáp án từ bảng GameAnswer
   * @param questionIds Danh sách ID câu hỏi
   * @returns Promise<VCNVGameAnswerAPI[]>
   */
  async getAnswersByQuestionIds(questionIds: string[]): Promise<VCNVGameAnswerAPI[]> {
    try {
      console.log(`[VCNVDA] Loading answers for questions: ${questionIds.join(', ')}`);

      const response: GetVCNVAnswersResponse = await this.answerController.getListSimple({
        query: `@GameQuestionId: {${questionIds.join(' | ')}}`,
      });

      if (response.code !== 200) {
        throw new Error(`API returned error code: ${response.code}`);
      }

      if (!response.data || response.data.length === 0) {
        throw new Error('No answers found for the specified questions');
      }

      return response.data;
    } catch (error) {
      console.error('[VCNVDA] Error loading answers:', error);
      throw error;
    }
  }
s

  /**
   * Transform API data thành game data
   * @param questions Danh sách câu hỏi từ API
   * @param answers Danh sách đáp án từ API
   * @returns Promise<VCNVQuestion[]>
   */
  async transformToGameData(
    questions: VCNVGameQuestionAPI[],
    answers: VCNVGameAnswerAPI[]
  ): Promise<VCNVQuestion[]> {
    try {
      console.log('[VCNVDA] Transforming API data to game data');
      const transformedQuestions: VCNVQuestion[] = [];

      for (const question of questions) {
        // Tìm answer tương ứng với question
        const questionAnswer = answers.find(a => a.GameQuestionId === question.Id);
        if (!questionAnswer) {
          throw new Error(`No answer found for question ${question.Id}`);
        }

        let words: VCNVWord[] = [];
        let keywordAnswer: string | undefined;

        if (question.Sort === 9) {
          // Câu hỏi từ khóa dọc - không cần split, chỉ lưu đáp án
          keywordAnswer = questionAnswer.Name;
        } else {
          // Câu hỏi hàng ngang - split thành từ
          const splitResult = await BaseDA.splitJanpanese(questionAnswer.Name);
          console.log(`[VCNVDA Debug] Question ${question.Id}: Original text="${questionAnswer.Name}", Split tokens=${splitResult?.tokens?.length || 0}, Sort=${questionAnswer.Sort}`);
          words = splitResult?.tokens?.map((token: any, index: number) => ({
            id: `${question.Id}_${index}`,
            text: token.surface,
            correctPosition: index + 1,
            isKey: (index + 1) === questionAnswer.Sort, // Position bắt đầu từ 1
          })) || [];
        }

        transformedQuestions.push({
          id: question.Id,
          questionText: question.Name,
          sort: question.Sort,
          words,
          keywordAnswer,
          stage: question.Stage,
          competenceId: question.Purpose,
          hint: question.Suggest,
        });
      }

      return transformedQuestions;
    } catch (error) {
      console.error('[VCNVDA] Error transforming data:', error);
      throw error;
    }
  }

  /**
   * Tạo crossword data từ transformed questions
   * @param questions Danh sách câu hỏi đã transform
   * @returns VCNVCrosswordData
   */
  static createCrosswordData(questions: VCNVQuestion[]): VCNVCrosswordData {
    try {
      console.log('[VCNVDA] Creating crossword data');

      // Tách câu hỏi hàng ngang và từ khóa dọc
      const horizontalQuestions = questions.filter(q => q.sort >= 1 && q.sort <= 8);
      const keywordQuestion = questions.find(q => q.sort === 9);

      if (!keywordQuestion) {
        throw new Error('Keyword question not found');
      }

      if (horizontalQuestions.length !== 8) {
        throw new Error(`Expected 8 horizontal questions, got ${horizontalQuestions.length}`);
      }

      // Tạo grid layout 8x9 (8 hàng, 9 cột)
      const gridLayout: VCNVGridCell[][] = [];
      
      for (let row = 0; row < 8; row++) {
        const rowCells: VCNVGridCell[] = [];
        const question = horizontalQuestions[row];
        
        for (let col = 0; col < 9; col++) {
          const cell: VCNVGridCell = {
            row,
            col,
            isKeyColumn: false,
            isActive: false,
          };

          // Xác định vị trí key column cho hàng này
          const keyWordIndex = question.words.findIndex(w => w.isKey);
          if (keyWordIndex !== -1) {
            const keyColumn = col - (question.words.length - keyWordIndex - 1);
            if (keyColumn >= 0 && keyColumn < question.words.length) {
              cell.isKeyColumn = (keyWordIndex === keyColumn);
            }
          }

          // Xác định ô có được sử dụng không
          const wordStartCol = Math.max(0, 9 - question.words.length);
          if (col >= wordStartCol) {
            cell.isActive = true;
            cell.questionId = question.id;
            cell.wordIndex = col - wordStartCol;
          }

          rowCells.push(cell);
        }
        
        gridLayout.push(rowCells);
      }

      return {
        horizontalQuestions,
        keywordQuestion,
        gridLayout,
      };
    } catch (error) {
      console.error('[VCNVDA] Error creating crossword data:', error);
      throw error;
    }
  }

  /**
   * Load complete game data
   * @param gameId ID của game
   * @param stage Stage hiện tại
   * @param competenceId ID competence
   * @returns Promise<VCNVCrosswordData>
   */
  async loadCompleteGameData(
    gameId: string,
    stage: number,
    competenceId: string
  ): Promise<VCNVCrosswordData> {
    try {
      console.log(`[VCNVDA] Loading complete game data for GameId: ${gameId}`);

      // Load questions
      const questions = await this.getQuestionsByGameAndStage(gameId, stage, competenceId);
      
      // Load answers
      const questionIds = questions.map(q => q.Id);
      const answers = await this.getAnswersByQuestionIds(questionIds);
debugger;
      // Transform data
      const transformedQuestions = await this.transformToGameData(questions, answers);

      // Create crossword data
      const crosswordData = VCNVDA.createCrosswordData(transformedQuestions);

      return crosswordData;
    } catch (error) {
      console.error('[VCNVDA] Error loading complete game data:', error);
      throw error;
    }
  }
}
