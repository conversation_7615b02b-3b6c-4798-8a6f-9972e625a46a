import {createSlice, Dispatch, PayloadAction} from '@reduxjs/toolkit';
import {ALTPQuestion, moneyLevels as defaultMoneyLevels} from '../data/questions';
import {getQuestionSetById, formatMoney} from '../data/questionSets';
import {DataController} from '../../../../base/baseController';
import {getDataToAsyncStorage} from '../../../../utils/AsyncStorage';
import ConfigAPI from '../../../../Config/ConfigAPI';
import {GameStatus, StorageContanst} from '../../../../Config/Contanst';
import {randomGID} from '../../../../utils/Utils';
import {milestonePositions} from '../data/milestonePositions';
interface Milestone {
  id: number;
  top: number;
  left: number;
  status: number;
  levelName?: string;
}
// Đ<PERSON>nh nghĩa trạng thái của game
export interface GameState {
  currentQuestionIndex: number;
  currentMoney: number;
  Milestone: Milestone[];
  loading: boolean;
  currentMilestoneId: number | null;
  guaranteedMoney: number;
  selectedAnswer: number | null;
  isAnswerCorrect: boolean | null;
  isGameOver: boolean;
  isGameWon: boolean;
  questions: ALTPQuestion[];
  availableLifelines: {
    fiftyFifty: boolean;
    phoneCall: boolean;
    audience: boolean;
    expert: boolean;
  };
  eliminatedOptions: number[];
  audiencePercentages: number[] | null;
  expertAnswer: number | null;
  phoneCallAnswer: number | null;
  highestScore: number;
  // Thêm các trường mới cho việc lấy câu hỏi từ API
  isLoadingQuestions: boolean;
  questionError: string | null;
  apiQuestions: any[]; // Dữ liệu thô từ API
  // Thêm trường mới cho moneyLevels từ API
  moneyLevels: number[];
}

// Trạng thái ban đầu
const initialState: GameState = {
  currentQuestionIndex: 0,
  Milestone: [],
  loading: false,
  currentMoney: 0,
  guaranteedMoney: 0,
  selectedAnswer: null,
  currentMilestoneId: null,
  isAnswerCorrect: null,
  isGameOver: false,
  isGameWon: false,
  questions: [],
  availableLifelines: {
    fiftyFifty: true,
    phoneCall: true,
    audience: true,
    expert: true,
  },
  eliminatedOptions: [],
  audiencePercentages: null,
  expertAnswer: null,
  phoneCallAnswer: null,
  highestScore: 0,
  // Thêm các trường mới
  isLoadingQuestions: false,
  questionError: null,
  apiQuestions: [],
  // Thêm trường moneyLevels với giá trị mặc định
  moneyLevels: defaultMoneyLevels,
};

// Tạo slice
export const gameSlice = createSlice({
  name: 'game',
  initialState,
  reducers: {
    setMilestones: (state, action: PayloadAction<Milestone[]>) => {
      state.Milestone = action.payload;
      state.loading = false;
    },
    setLoading: state => {
      state.loading = true;
    },
    setCurrentMilestoneId: (state, action: PayloadAction<number>) => {
      state.currentMilestoneId = action.payload;
    },
    // Thêm các action mới cho việc lấy câu hỏi từ API
    fetchQuestionsRequest: (state) => {
      state.isLoadingQuestions = true;
      state.questionError = null;
    },
    fetchQuestionsSuccess: (state, action: PayloadAction<any[]>) => {
      state.isLoadingQuestions = false;
      state.apiQuestions = action.payload;
    },
    fetchQuestionsFailure: (state, action: PayloadAction<string>) => {

      state.isLoadingQuestions = false;
      state.questionError = action.payload;
    },
    setProcessedQuestions: (state, action: PayloadAction<ALTPQuestion[]>) => {
      state.questions = action.payload;
    },
    // Thêm action mới cho việc cập nhật moneyLevels
    setMoneyLevels: (state, action: PayloadAction<number[]>) => {
      state.moneyLevels = action.payload;
    },
    // Khởi tạo game với bộ câu hỏi
    initializeGame: (state, action: PayloadAction<string>) => {
      const questionSetId = action.payload;
      const questionSet = getQuestionSetById(questionSetId);

      if (questionSet) {
        state.questions = questionSet.questions;
        state.currentQuestionIndex = 0;
        state.currentMoney = 0;
        state.guaranteedMoney = 0;
        state.selectedAnswer = null;
        state.isAnswerCorrect = null;
        state.isGameOver = false;
        state.isGameWon = false;
        state.availableLifelines = {
          fiftyFifty: true,
          phoneCall: true,
          audience: true,
          expert: true,
        };
        state.eliminatedOptions = [];
        state.audiencePercentages = null;
        state.expertAnswer = null;
        state.phoneCallAnswer = null;
      }
    },

    // Chọn đáp án
    selectAnswer: (state, action: PayloadAction<number>) => {
      state.selectedAnswer = action.payload;

      const currentQuestion = state.questions[state.currentQuestionIndex];
      state.isAnswerCorrect =
        action.payload === currentQuestion.correctAnswerIndex;

      if (state.isAnswerCorrect) {
        state.currentMoney = currentQuestion.moneyValue;

        // Cập nhật mức tiền đảm bảo theo các mốc 5, 10, 15
        // Mốc 1: Câu 5 (index 4)
        // Mốc 2: Câu 10 (index 9)
        // Mốc 3: Câu 15 (index 14)
        if (state.currentQuestionIndex === 4) {
          state.guaranteedMoney = currentQuestion.moneyValue; // Mốc câu 5
        } else if (state.currentQuestionIndex === 9) {
          state.guaranteedMoney = currentQuestion.moneyValue; // Mốc câu 10
        } else if (state.currentQuestionIndex === 14) {
          state.guaranteedMoney = currentQuestion.moneyValue; // Mốc câu 15
        }

        // Kiểm tra nếu đã thắng game (trả lời đúng câu hỏi cuối cùng)
        if (state.currentQuestionIndex === state.questions.length - 1) {
          state.isGameWon = true;
          state.isGameOver = true;

          // Cập nhật điểm cao nhất
          if (state.currentMoney > state.highestScore) {
            state.highestScore = state.currentMoney;
          }
        }
      } else {
        state.isGameOver = true;

        // Nếu thua, quay về mức tiền đảm bảo dựa trên câu hỏi hiện tại
        // Nếu chưa qua mốc nào (< câu 5), không được tiền
        // Nếu qua mốc 1 (>= câu 5) nhưng chưa qua mốc 2 (< câu 10), nhận tiền ở mốc 1
        // Nếu qua mốc 2 (>= câu 10) nhưng chưa qua mốc 3 (< câu 15), nhận tiền ở mốc 2
        // Nếu qua mốc 3 (câu 15), nhận tiền ở mốc 3
        if (state.currentQuestionIndex < 4) {
          // Chưa qua mốc nào
          state.currentMoney = 0;
        } else if (
          state.currentQuestionIndex >= 4 &&
          state.currentQuestionIndex < 9
        ) {
          // Qua mốc 1 (câu 5)
          state.currentMoney = state.questions[4].moneyValue; // Tiền ở câu 5
        } else if (
          state.currentQuestionIndex >= 9 &&
          state.currentQuestionIndex < 14
        ) {
          // Qua mốc 2 (câu 10)
          state.currentMoney = state.questions[9].moneyValue; // Tiền ở câu 10
        } else {
          // Qua mốc 3 (câu 15)
          state.currentMoney = state.questions[14].moneyValue; // Tiền ở câu 15
        }

        // Cập nhật điểm cao nhất
        if (state.currentMoney > state.highestScore) {
          state.highestScore = state.currentMoney;
        }
      }
    },

    // Chuyển sang câu hỏi tiếp theo
    nextQuestion: state => {
      if (state.currentQuestionIndex < state.questions.length - 1) {
        // Log trước khi cập nhật
        console.log(
          'nextQuestion: Trước khi cập nhật, currentQuestionIndex =',
          state.currentQuestionIndex,
        );
        console.log(
          'nextQuestion: Câu hỏi hiện tại:',
          state.questions[state.currentQuestionIndex]?.question,
        );

        // Cập nhật index
        state.currentQuestionIndex += 1;

        // Log sau khi cập nhật
        console.log(
          'nextQuestion: Sau khi cập nhật, currentQuestionIndex =',
          state.currentQuestionIndex,
        );
        console.log(
          'nextQuestion: Câu hỏi mới:',
          state.questions[state.currentQuestionIndex]?.question,
        );

        // Reset các state khác
        state.selectedAnswer = null;
        state.isAnswerCorrect = null;
        state.eliminatedOptions = [];
        state.audiencePercentages = null;
        state.expertAnswer = null;
        state.phoneCallAnswer = null;
      } else {
        console.log(
          'nextQuestion: Đã đến câu hỏi cuối cùng, không thể chuyển tiếp',
        );
      }
    },

    // Sử dụng quyền trợ giúp
    applyLifeline: (state, action: PayloadAction<string>) => {
      const lifeline = action.payload;
      const currentQuestion = state.questions[state.currentQuestionIndex];

      if (lifeline === 'fiftyFifty' && state.availableLifelines.fiftyFifty) {
        state.availableLifelines.fiftyFifty = false;

        // Loại bỏ 2 đáp án sai
        const wrongOptions = [0, 1, 2, 3].filter(
          index => index !== currentQuestion.correctAnswerIndex,
        );

        // Chọn ngẫu nhiên 2 đáp án sai để loại bỏ
        const shuffledWrongOptions = wrongOptions.sort(
          () => Math.random() - 0.5,
        );
        state.eliminatedOptions = [
          shuffledWrongOptions[0],
          shuffledWrongOptions[1],
        ];
      } else if (
        lifeline === 'phoneCall' &&
        state.availableLifelines.phoneCall
      ) {
        state.availableLifelines.phoneCall = false;

        // 80% cơ hội trả lời đúng
        if (Math.random() < 0.8) {
          state.phoneCallAnswer = currentQuestion.correctAnswerIndex;
        } else {
          // 20% cơ hội trả lời sai
          const wrongOptions = [0, 1, 2, 3].filter(
            index => index !== currentQuestion.correctAnswerIndex,
          );
          state.phoneCallAnswer =
            wrongOptions[Math.floor(Math.random() * wrongOptions.length)];
        }
      } else if (lifeline === 'audience' && state.availableLifelines.audience) {
        state.availableLifelines.audience = false;

        // Tạo phần trăm cho từng đáp án
        const percentages = [0, 0, 0, 0];

        // Đáp án đúng có tỷ lệ cao hơn
        const correctIndex = currentQuestion.correctAnswerIndex;

        // Tỷ lệ cho đáp án đúng (40-70%)
        percentages[correctIndex] = Math.floor(Math.random() * 30) + 40;

        // Phân bổ phần trăm còn lại cho các đáp án sai
        const remainingPercentage = 100 - percentages[correctIndex];
        const wrongOptions = [0, 1, 2, 3].filter(
          index => index !== correctIndex,
        );

        // Phân bổ ngẫu nhiên
        let remaining = remainingPercentage;
        for (let i = 0; i < wrongOptions.length - 1; i++) {
          const randomPercent = Math.floor(Math.random() * remaining);
          percentages[wrongOptions[i]] = randomPercent;
          remaining -= randomPercent;
        }

        // Đáp án sai cuối cùng nhận phần còn lại
        percentages[wrongOptions[wrongOptions.length - 1]] = remaining;

        state.audiencePercentages = percentages;
      } else if (lifeline === 'expert' && state.availableLifelines.expert) {
        state.availableLifelines.expert = false;

        // 90% cơ hội trả lời đúng
        if (Math.random() < 0.9) {
          state.expertAnswer = currentQuestion.correctAnswerIndex;
        } else {
          // 10% cơ hội trả lời sai
          const wrongOptions = [0, 1, 2, 3].filter(
            index => index !== currentQuestion.correctAnswerIndex,
          );
          state.expertAnswer =
            wrongOptions[Math.floor(Math.random() * wrongOptions.length)];
        }
      }
    },

    // Kết thúc game và lấy tiền
    takeMoneyAndQuit: state => {
      state.isGameOver = true;

      // Khi người chơi chủ động dừng cuộc chơi, họ nhận được số tiền hiện tại
      // Không cần quay về mức tiền đảm bảo

      // Cập nhật điểm cao nhất
      if (state.currentMoney > state.highestScore) {
        state.highestScore = state.currentMoney;
      }
    },

    // Đặt lại game
    resetGame: state => {
      return {
        ...initialState,
        highestScore: state.highestScore, // Giữ lại điểm cao nhất
        Milestone: state.Milestone, // Giữ lại milestone data
        moneyLevels: state.moneyLevels, // Giữ lại moneyLevels data
        apiQuestions: state.apiQuestions, // Giữ lại apiQuestions data
        questions: state.questions, // Giữ lại questions data
        currentMilestoneId: state.currentMilestoneId, // Giữ lại currentMilestoneId
      };
    },

    // Cập nhật điểm cao nhất từ AsyncStorage
    setHighestScore: (state, action: PayloadAction<number>) => {
      state.highestScore = action.payload;
    },
  },
});

// Export actions
export const {
  initializeGame,
  selectAnswer,
  nextQuestion,
  setLoading,
  setMilestones,
  setCurrentMilestoneId,
  applyLifeline,
  takeMoneyAndQuit,
  resetGame,
  setHighestScore,
  // Thêm các action mới
  fetchQuestionsRequest,
  fetchQuestionsSuccess,
  fetchQuestionsFailure,
  setProcessedQuestions,
  setMoneyLevels,
} = gameSlice.actions;

// Export reducer
export default gameSlice.reducer;

// Hàm thunk để lấy câu hỏi từ API
export const fetchQuestions = (milestoneId: number, competenceId: string) => async (dispatch: Dispatch, getState: any) => {
  try {
    dispatch(fetchQuestionsRequest());
    // Lấy dữ liệu từ API
    const GameQuestionController = new DataController('GameQuestion');
    const GameAnswerQuestionController = new DataController('GameAnswer');
    const response = await GameQuestionController.getListSimple({
      query: `@Stage: [${milestoneId}] @Purpose: [${competenceId}] @GameId: {${ConfigAPI.gameALTP}} `,
    });

    if (response && response.data && response.data.length > 0) {

      const answerResponse = await GameAnswerQuestionController.getListSimple({
        query: `@GameQuestionId: {${response.data.map((q: any) => q.Id).join(' | ')}}`,
      });
      if (answerResponse && answerResponse.data && answerResponse.data.length > 0) {
        // Map các câu trả lời vào câu hỏi
        response.data.forEach((question: any) => {
          //xử lý random đảo đáp án trả lời
          const shuffledAnswers = shuffleArray(answerResponse.data.filter((answer: any) => answer.GameQuestionId === question.Id));
          question.Answers = shuffledAnswers;
        });
      }

      // Lưu dữ liệu thô
      //lấy danh sách cấp độ
      // Lưu dữ liệu đã cập nhật Level
      dispatch(fetchQuestionsSuccess(response.data));

      // Lấy moneyLevels từ Redux store
      const state = getState();
      const moneyLevels = state.game.moneyLevels;

      // Xử lý dữ liệu và tạo bộ câu hỏi với moneyLevels từ Redux store
      const questions = processQuestions(response.data, moneyLevels);

      dispatch(setProcessedQuestions(questions));
    } else {
      dispatch(fetchQuestionsFailure('Không có dữ liệu câu hỏi'));
    }
  } catch (error) {
    console.error('Lỗi khi lấy câu hỏi:', error);
    dispatch(fetchQuestionsFailure('Lỗi khi lấy câu hỏi'));
  }
};

// Hàm xử lý và random câu hỏi
const processQuestions = (apiQuestions: any[], moneyLevels: number[] = defaultMoneyLevels): ALTPQuestion[] => {
  // Phân loại câu hỏi theo cấp độ
  const level1Questions = apiQuestions.filter(q => q.Level === 1);
  const level2Questions = apiQuestions.filter(q => q.Level === 2);
  const level3Questions = apiQuestions.filter(q => q.Level === 3);

  console.log(`Số câu hỏi cấp độ 1: ${level1Questions.length}`);
  console.log(`Số câu hỏi cấp độ 2: ${level2Questions.length}`);
  console.log(`Số câu hỏi cấp độ 3: ${level3Questions.length}`);

  // Shuffle các mảng câu hỏi
  const shuffledLevel1 = shuffleArray(level1Questions);
  const shuffledLevel2 = shuffleArray(level2Questions);
  const shuffledLevel3 = shuffleArray(level3Questions);

  // Lấy số lượng câu hỏi theo yêu cầu: 5 câu mỗi cấp độ
  const selectedLevel1 = shuffledLevel1.slice(0, 5);
  const selectedLevel2 = shuffledLevel2.slice(0, 5);
  const selectedLevel3 = shuffledLevel3.slice(0, 5);

  // Kết hợp các câu hỏi đã chọn
  const combinedQuestions = [
    ...selectedLevel1,
    ...selectedLevel2,
    ...selectedLevel3,
  ];

  // Gán giá trị tiền thưởng cho từng câu hỏi
  return combinedQuestions.map((q, index) => ({
    id: q.Id,
    question: q.Name,
    options: q.Answers.map((answer: any) => answer.Name),
    // correctAnswerIndex: getCorrectAnswerIndex(q),
    correctAnswerIndex: q.Answers.findIndex((answer: any) => answer.IsResult === true),
    moneyValue: moneyLevels[index], // Sử dụng moneyLevels từ tham số
  }));
};

// Hàm lấy index của đáp án đúng đã được thay thế bằng CorrectAnswerIndex từ API

// Hàm shuffle mảng
const shuffleArray = <T>(array: T[]): T[] => {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
};

// Hàm thunk để lấy moneyLevels từ API
export const fetchMoneyLevels = (gameId: string) => async (dispatch: Dispatch) => {
  try {
    // Lấy dữ liệu từ API
    const gameConfigController = new DataController('GameConfig');
    const response = await gameConfigController.getListSimple({
      query: `@GameId: {${gameId}}`,
      sortby: {BY: 'Sort', DIRECTION: 'ASC'},
    });

    if (response && response.data && response.data.length > 0) {
      // Xử lý dữ liệu từ API
      try {
        const moneyLevelsData = response.data.map((item: any) => parseInt(item.Score, 10));

        // Cập nhật moneyLevels trong Redux store
        dispatch(setMoneyLevels(moneyLevelsData));
      } catch (error) {
        console.error('Lỗi khi parse moneyLevels từ API:', error);
        // Sử dụng giá trị mặc định từ file questions.ts
        dispatch(setMoneyLevels(defaultMoneyLevels));
      }
    } else {
      console.warn('Không tìm thấy cấu hình moneyLevels, sử dụng giá trị mặc định');
      // Sử dụng giá trị mặc định từ file questions.ts
      dispatch(setMoneyLevels(defaultMoneyLevels));
    }
  } catch (error) {
    console.error('Lỗi khi lấy moneyLevels từ API:', error);
    // Sử dụng giá trị mặc định từ file questions.ts
    dispatch(setMoneyLevels(defaultMoneyLevels));
  }
};

//tạo game action để lấy thông tin game
export class gameAction {
  //lấy data minstone. mặc định sẽ luôn có 7 chặng. chỉ lấy data ra để kiểm tra xem user đã thực hiện chơi chặng nào và lấy status các chặng tương ứng. nếu chưa chơi thì mặc định chặng đầu tiên sẽ in-progress
  static getMilestones = (gameId: string) => async (dispatch: Dispatch) => {
    try {
      dispatch(setLoading());
      const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
      if (!cusId) {
        // Nếu không có CustomerId, vẫn hiển thị milestone mặc định với milestone đầu tiên là Pending
        const defaultMilestones = this.getDefaultMilestones();
        dispatch(setMilestones(defaultMilestones));
        return defaultMilestones;
      }

      // Lấy dữ liệu từ API
      const gameCustomerController = new DataController('GameCustomer');
      const response = await gameCustomerController.getListSimple({
        query: `@CustomerId: {${cusId}} @GameId: {${gameId}}`,
      });

      // Lấy danh sách milestone mặc định
      const initialMilestones = this.getDefaultMilestones();

      // Nếu không có dữ liệu từ API
      if (!response.data || response.data.length === 0) {
        // Nếu chưa có data thì trả về danh sách chặng mặc định với chặng đầu tiên là in-progress
        initialMilestones[0].status = GameStatus.Pending;
        dispatch(setMilestones(initialMilestones));
        return initialMilestones;
      }

      // Nếu có dữ liệu từ API, xử lý dữ liệu
      console.log('Dữ liệu milestone từ API:', response.data);

      // Tìm các stage đã completed (Status = 1)
      const completedStages = new Set<number>();

      response.data.forEach((item: any) => {
        const stage = item.Stage;
        const status = item.Status;

        // Nếu có record với Status = 1 (completed) thì stage đó đã hoàn thành
        if (status === GameStatus.Completed) { // GameStatus.Completed = 1
          completedStages.add(stage);
        }
      });

      // Tìm stage cao nhất đã completed
      let highestCompletedStage = 0;
      completedStages.forEach(stage => {
        if (stage > highestCompletedStage) {
          highestCompletedStage = stage;
        }
      });

      // Cập nhật trạng thái cho tất cả các milestone
      const updatedMilestones = initialMilestones.map(milestone => {
        const stageId = milestone.id;

        if (completedStages.has(stageId)) {
          // Stage đã completed
          return {
            ...milestone,
            status: GameStatus.Completed,
          };
        } else if (stageId === 1 && highestCompletedStage === 0) {
          // Milestone đầu tiên và chưa có stage nào completed
          return {
            ...milestone,
            status: GameStatus.Pending,
          };
        } else if (stageId === highestCompletedStage + 1) {
          // Stage tiếp theo sau stage completed cao nhất
          return {
            ...milestone,
            status: GameStatus.Pending,
          };
        } else {
          // Các stage còn lại là Locked
          return {
            ...milestone,
            status: GameStatus.Locked,
          };
        }
      });

      console.log('Milestone đã cập nhật:', updatedMilestones);
      dispatch(setMilestones(updatedMilestones));
      return updatedMilestones;
    } catch (error) {
      console.error('Lỗi khi lấy dữ liệu milestone:', error);

      // Nếu có lỗi, vẫn hiển thị milestone mặc định với milestone đầu tiên là Pending
      const defaultMilestones = this.getDefaultMilestones();
      defaultMilestones[0].status = GameStatus.Pending;
      dispatch(setMilestones(defaultMilestones));
      return defaultMilestones;
    }
  };

  // Hàm trả về danh sách milestone mặc định
  private static getDefaultMilestones(): Milestone[] {
    // Sử dụng vị trí milestone từ file milestonePositions.ts
    return milestonePositions.map(pos => ({
      id: pos.id,
      top: pos.top,
      left: pos.left,
      status: GameStatus.Locked,
      levelName: pos.levelName,
    }));
  }

  // Cập nhật trạng thái milestone
  static updateMilestoneStatus =
    (milestoneId: number, status: number, score: number, gameId: string) =>
    async (dispatch: Dispatch, getState: any) => {
      try {
        console.log(
          `Cập nhật milestone ${milestoneId} với trạng thái ${status} và điểm ${score}`,
        );

        const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
        if (!cusId) {
          console.error('Không tìm thấy CustomerId');
          return null;
        }

        // Lấy state hiện tại để kiểm tra trạng thái hiện tại của milestone
        const state = getState();
        const currentMilestone = state.game.Milestone.find((m: any) => m.id === milestoneId);
        // Nếu milestone đã hoàn thành (Completed) trước đó và status mới là Pending,
        // giữ nguyên trạng thái Completed
        let newStatus = status;
        if (currentMilestone &&
            currentMilestone.status === GameStatus.Completed &&
            status === GameStatus.Pending) {
          console.log(`Milestone ${milestoneId} đã hoàn thành trước đó, giữ nguyên trạng thái Completed`);
          newStatus = GameStatus.Completed;
        }

        // Tìm game hiện tại

        // Cập nhật trực tiếp state trong Redux thay vì gọi lại API
        // Tìm milestone cần cập nhật
        if (state.game.Milestone && state.game.Milestone.length > 0) {
          // Tạo bản sao của mảng milestone
          const updatedMilestones = [...state.game.Milestone];

          // Tìm và cập nhật milestone
          const milestoneIndex = updatedMilestones.findIndex(
            m => m.id === milestoneId,
          );
          if (milestoneIndex !== -1) {
            // Nếu milestone đã hoàn thành trước đó và status mới là Pending,
            // giữ nguyên trạng thái Completed
            if (updatedMilestones[milestoneIndex].status === GameStatus.Completed &&
                status === GameStatus.Pending) {
              console.log(`Không cập nhật milestone ${milestoneId} từ Completed thành Pending`);
            } else {
              // Cập nhật trạng thái
              updatedMilestones[milestoneIndex] = {
                ...updatedMilestones[milestoneIndex],
                status: newStatus,
              };
            }

            // Nếu milestone đã hoàn thành, cập nhật trạng thái của milestone tiếp theo
            if (newStatus === GameStatus.Completed) {
              // Tìm milestone tiếp theo
              const nextMilestoneId = milestoneId + 1;
              const nextMilestoneIndex = updatedMilestones.findIndex(
                m => m.id === nextMilestoneId,
              );

              if (nextMilestoneIndex !== -1) {
                // Nếu milestone tiếp theo tồn tại, đặt trạng thái là Pending
                updatedMilestones[nextMilestoneIndex] = {
                  ...updatedMilestones[nextMilestoneIndex],
                  status: GameStatus.Pending,
                };

                console.log(`Đã mở khóa milestone ${nextMilestoneId}`);
              }
            }

            console.log('Milestone đã cập nhật:', updatedMilestones);

            // Dispatch action để cập nhật state
            dispatch(setMilestones(updatedMilestones));
          } else {
            console.warn(
              `Không tìm thấy milestone với ID ${milestoneId} trong state`,
            );
            // Nếu không tìm thấy milestone trong state, gọi lại API để lấy danh sách mới
            await gameAction.getMilestones(gameId)(dispatch);
          }
        } else {
          console.warn('Không có milestone trong state');
          // Nếu chưa có milestone trong state, gọi API để lấy danh sách
          await gameAction.getMilestones(gameId)(dispatch);
        }

        return true;
      } catch (error) {
        console.error('Lỗi khi cập nhật trạng thái milestone:', error);
        return false;
      }
    };
    //lấy danh sách câu hỏi theo chặng
    static getQuestionsByMilestone = (milestoneId: number, gameId: string, competenceId: string) => async (dispatch: Dispatch) => {
      try {
        const controller = new DataController('GameQuizQuestion');
        const questions = await controller.getListSimple({
          query: `@Stage: {${milestoneId}} @GameId: {${gameId}} @GameCompetenceId: {${competenceId}}`,
        });
        dispatch(initializeGame(questions));
      } catch (error) {
        console.error('Lỗi khi lấy danh sách câu hỏi:', error);
      }
    };

}
