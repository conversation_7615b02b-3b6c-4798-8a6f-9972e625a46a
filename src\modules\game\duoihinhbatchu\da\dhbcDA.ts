import {DataController} from '../../../../base/baseController';

// Interface cho GameQuestion API response
export interface DHBCGameQuestionAPI {
  Id: string;
  GameId: string;
  Stage: number;
  Name: string;        // Text câu hỏi
  Img: string;         // URL hình ảnh
  Purpose: string;     // CompetenceId
  Sort?: number;
  IsActive: boolean;
  CreatedDate?: string;
  UpdatedDate?: string;
  Suggest: string;
}

// Interface cho GameAnswer API response
export interface DHBCGameAnswerAPI {
  Id: string;
  GameQuestionId: string;
  Name: string;        // Đáp án đúng
  IsCorrect: boolean;  // Để hiển thị đúng/sai
  Sort?: number;
  IsActive: boolean;
  CreatedDate?: string;
  UpdatedDate?: string;
}

// Interface cho GameConfig API response
export interface DHBCGameConfigAPI {
  Id: string;
  GameId: string;
  Score: number;        // Điểm trên mỗi mạng
  LifeCount: number;    // <PERSON><PERSON> mạng chơi
  Time: number;         // Thời gian chơi (giây)
  Bonus: number;        // Điểm bonus khi hoàn thành không mất mạng
  IsActive: boolean;
  CreatedDate?: string;
  UpdatedDate?: string;
  ScoreHint: number;
}

// Interface cho question đã transform để sử dụng trong game
export interface DHBCQuestion {
  id: string;
  text: string;
  image: string;
  hint: string;     
  answer: string;
}

// Interface cho game config đã transform
export interface DHBCGameConfig {
  gameId: string;
  scorePerLife: number;
  maxLives: number;
  timeLimit: number;
  bonusScore: number;
  isActive: boolean;
  gemHint: number
}

// API Response interface
export interface ApiResponse<T> {
  code: number;
  data: T[];
  message?: string;
}

export class DHBCDA {
  private questionController: DataController;
  private answerController: DataController;
  private configController: DataController;

  constructor() {
    this.questionController = new DataController('GameQuestion');
    this.answerController = new DataController('GameAnswer');
    this.configController = new DataController('GameConfig');
  }

  /**
   * Lấy danh sách câu hỏi từ bảng GameQuestion và GameAnswer
   * @param gameId ID của game DHBC
   * @param stage Stage hiện tại
   * @param competenceId ID của competence
   * @returns Promise<DHBCQuestion[]>
   */
  async getQuestionsByGameAndStage(
    gameId: string,
    stage: number,
    competenceId: string,
  ): Promise<DHBCQuestion[]> {
    try {
      console.log(`[DHBCDA] Loading questions for GameId: ${gameId}, Stage: ${stage}, CompetenceId: ${competenceId}`);
      
      // Lấy danh sách câu hỏi
      const questionResponse: ApiResponse<DHBCGameQuestionAPI> =
        await this.questionController.getListSimple({
          query: `@GameId: {${gameId}} @Stage: [${stage}] @Purpose: [${competenceId}]`,
          sortby: {BY: 'Sort', DIRECTION: 'ASC'},
        });

      if (questionResponse.code !== 200 || !questionResponse.data || questionResponse.data.length === 0) {
        console.warn('[DHBCDA] No questions found');
        return [];
      }

      // Lấy danh sách đáp án cho tất cả câu hỏi
      const questionIds = questionResponse.data.map(q => q.Id);
      const answerResponse: ApiResponse<DHBCGameAnswerAPI> =
        await this.answerController.getListSimple({
          query: `@GameQuestionId: {${questionIds.join(' | ')}}`,
        });

      if (answerResponse.code !== 200 || !answerResponse.data) {
        console.warn('[DHBCDA] No answers found');
        return [];
      }

      // Transform data thành format game cần
      const transformedQuestions: DHBCQuestion[] = questionResponse.data.map(question => {
        // Tìm đáp án cho câu hỏi này
        
        const answer = answerResponse.data.find(ans => ans.GameQuestionId === question.Id);
        
        if (!answer) {
          console.warn(`[DHBCDA] No answer found for question ${question.Id}`);
          return null;
        }

        return {
          id: question.Id,
          text: question.Name || '',
          image: question.Img || '',
          hint: question.Suggest, 
          answer: answer.Name.toLowerCase() || '', // Convert to lowercase để so sánh
        };
      }).filter(q => q !== null) as DHBCQuestion[];

      console.log(`[DHBCDA] Successfully loaded ${transformedQuestions.length} questions`);
      return transformedQuestions;

    } catch (error) {
      console.error('[DHBCDA] Error loading questions:', error);
      throw error;
    }
  }

  /**
   * Lấy cấu hình game từ bảng GameConfig
   * @param gameId ID của game DHBC
   * @returns Promise<DHBCGameConfig>
   */
  static async getGameConfig(gameId: string): Promise<DHBCGameConfig> {
    try {
      console.log(`[DHBCDA] Loading game config for GameId: ${gameId}`);
      
      const controller = new DataController('GameConfig');
      const response: ApiResponse<DHBCGameConfigAPI> = await controller.getListSimple({
        query: `@GameId: {${gameId}}`,
      });

      if (response.code !== 200 || !response.data || response.data.length === 0) {
        throw new Error('No game config found or API returned unsuccessful response');
      }

      const configData = response.data[0];
      const transformedConfig: DHBCGameConfig = {
        gameId: gameId,
        scorePerLife: configData.Score || 10,
        maxLives: configData.LifeCount || 3,
        timeLimit: configData.Time || 300,
        bonusScore: configData.Bonus || 50,
        isActive: configData.IsActive || true,
        gemHint: configData.ScoreHint || 0,
      };

      console.log('[DHBCDA] Successfully loaded game config:', transformedConfig);
      return transformedConfig;

    } catch (error) {
      console.error('[DHBCDA] Error loading game config:', error);
      throw error;
    }
  }

  /**
   * Validate question data
   * @param question DHBCQuestion object
   * @returns boolean
   */
  private static validateQuestion(question: DHBCQuestion): boolean {
    if (!question.id || !question.text || !question.answer) {
      console.warn(`[DHBCDA] Invalid question data:`, question);
      return false;
    }
    return true;
  }
}
